<template>
  <a-popover
    v-model:open="popoverOpen"
    trigger="click"
    placement="bottom"
    overlayClassName="custom-select-popover"
    :get-popup-container="triggerNode => triggerNode.parentNode"
  >
    <template #content>
      <div class="popover-content" @mousedown.stop :style="{ width: `${width}px` }">
        <div class="list-container">
            <div v-if="filterable" class="search-wrapper">
              <input type="text" v-model="searchText" placeholder="搜索关键词" class="search-input" />
            </div>
            <div class="list-wrapper" ref="listWrapperRef" :style="{ maxHeight: `${maxHeight}px` }">
              <template v-if="loading">
                <div class="list-loading">
                  <a-spin />
                </div>
              </template>
              <template v-else-if="datasource.length > 0">
                <div
                  v-for="item in datasource"
                  :key="item.value"
                  class="list-item"
                  :class="{ active: modelValue === item.value }"
                  @click="handleItemSelect(item)"
                  :ref="el => { if(modelValue === item.value) activeItemRef = el }"
                >
                  {{ item.text }}
                </div>
              </template>
               <div v-else class="list-empty">
                无匹配数据
              </div>
            </div>
        </div>
        <div class="pageBar" v-if="showPageBar">
            <img :src="isAtTop ? pageUpDisabled : pageUp"  
                @mousedown="startScrolling(-1)" 
                @mouseup="stopScrolling" 
                @mouseleave="stopScrolling"
                @touchstart="startScrolling(-1)"
                @touchend="stopScrolling"
                @touchcancel="stopScrolling" />
            <img :src="isAtBottom ? pageDownDisabled : pageDown" 
                @mousedown="startScrolling(1)" 
                @mouseup="stopScrolling" 
                @mouseleave="stopScrolling"
                @touchstart="startScrolling(1)"
                @touchend="stopScrolling"
                @touchcancel="stopScrolling" />
        </div>
      </div>
    </template>
    <slot></slot>
  </a-popover>
</template>

<script>
import { ref, computed, watch, nextTick } from 'vue';
import pageUp from '@/assets/pageUp.png';
import pageDown from '@/assets/pageDown.png';
import pageUpDisabled from '@/assets/pageUp_disable.png';
import pageDownDisabled from '@/assets/pageDown_disable.png';

export default {
  name: 'CustomSelect',
  props: {
    list: {
      type: Array,
      required: true,
      default: () => [],
    },
    filterable: {
      type: Boolean,
      default: false,
    },
    filterMethod: {
      type: Function,
      default: null
    },
    remote: {
      type: Boolean,
      default: false
    },
    remoteMethod: {
      type: Function,
      default: null
    },
    modelValue: {
        type: [String, Number],
        default: ''
    },
    maxHeight: {
        type: Number,
        default: 250
    },
    width: {
        type: Number,
        default: 250
    },
    debounce: {
      type: Number,
      default: 300
    }
  },
  emits: ['update:modelValue', 'select', 'change'],
  setup(props, { emit }) {
    const popoverOpen = ref(false);
    const searchText = ref('');
    const listWrapperRef = ref(null);
    const showPageBar = ref(false);
    const activeItemRef = ref(null);
    const loading = ref(false);
    const datasource = ref([]);
    let debounceTimer = null;

    const scrollStep = ref(30);
    const scrollInterval = ref(null);
    const scrollSpeed = ref(30);
    const isAtTop = ref(true);
    const isAtBottom = ref(false);

    const checkPageBarVisibility = () => {
        const container = listWrapperRef.value;
        if (container) {
            showPageBar.value = container.scrollHeight > container.clientHeight;
        }
    };

    const checkScrollPosition = () => {
        const container = listWrapperRef.value;
        if (container) {
            isAtTop.value = container.scrollTop <= 0;
            isAtBottom.value = Math.abs(container.scrollHeight - container.scrollTop - container.clientHeight) < 1;
        }
    };

    const scrollToActiveItem = () => {
        if (props.modelValue && activeItemRef.value && listWrapperRef.value) {
            const container = listWrapperRef.value;
            const activeItem = activeItemRef.value;
            
            // 计算滚动位置，使选中项居中显示
            const containerHeight = container.clientHeight;
            const itemTop = activeItem.offsetTop;
            const itemHeight = activeItem.offsetHeight;
            
            const scrollPosition = itemTop - (containerHeight / 2) + (itemHeight / 2);
            container.scrollTop = Math.max(0, scrollPosition);
            
            // 更新滚动状态
            nextTick(() => {
                checkScrollPosition();
            });
        }
    };

    // 执行默认筛选
    const defaultFilter = (item, query) => {
      if (!query) return true;
      return item.text.toLowerCase().includes(query.toLowerCase());
    };

    // 计算最终使用的筛选方法
    const finalFilterMethod = computed(() => {
      return props.filterMethod || defaultFilter;
    });

    // 更新列表并滚动到选中项
    const updateListAndScroll = (newList) => {
        datasource.value = newList;
        nextTick(() => {
            checkPageBarVisibility();
            checkScrollPosition();
            if (popoverOpen.value) {
                scrollToActiveItem();
            }
        });
    };

    // 远程搜索
    const handleRemoteSearch = (query) => {
      if (!props.remoteMethod) return;
      
      loading.value = true;
      
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
      
      debounceTimer = setTimeout(() => {
        props.remoteMethod(query).then(result => {
          loading.value = false;
          updateListAndScroll(result || []);
        }).catch(() => {
          loading.value = false;
          updateListAndScroll([]);
        });
      }, props.debounce);
    };

    // 监听搜索框输入
    watch(() => searchText.value, (val) => {
      if (!props.filterable) return;

      if (props.remote) {
        handleRemoteSearch(val);
      } else {
        const newList = props.list.filter(item => finalFilterMethod.value(item, val));
        updateListAndScroll(newList);
      }
    });

    // 页面初始化时，如果远程搜索模式，立即加载数据
    watch(popoverOpen, (isOpen) => {
        if (isOpen) {
            setTimeout(() => {
                if (props.remote && props.filterable) {
                    handleRemoteSearch(searchText.value);
                } else {
                    const newList = props.list.filter(item => finalFilterMethod.value(item, searchText.value));
                    updateListAndScroll(newList);
                }
                
                if (listWrapperRef.value) {
                    listWrapperRef.value.addEventListener('scroll', checkScrollPosition);
                }
            }, 100);
        } else {
             searchText.value = '';
             if (listWrapperRef.value) {
                listWrapperRef.value.removeEventListener('scroll', checkScrollPosition);
            }
        }
    });

    

    const startScrolling = (direction) => {
        if ((direction < 0 && isAtTop.value) || (direction > 0 && isAtBottom.value)) {
            return;
        }
        if (scrollInterval.value) clearInterval(scrollInterval.value);
        
        const scroll = () => {
            if(listWrapperRef.value){
                listWrapperRef.value.scrollTop += direction * scrollStep.value;
                checkScrollPosition(); // 滚动时实时更新状态
            }
        }
        scroll();
        scrollInterval.value = setInterval(scroll, scrollSpeed.value);
    };

    const stopScrolling = () => {
        if (scrollInterval.value) {
            clearInterval(scrollInterval.value);
            scrollInterval.value = null;
        }
    };
    
    const handleItemSelect = (item) => {
      emit('select', item);

      if (props.modelValue !== item.value) {
        emit('change', item.value, item);
        emit('update:modelValue', item.value);
      }
      
      popoverOpen.value = false;
    };

    // 监听modelValue变化，如果弹窗打开状态下值变化了，需要滚动到新选中项
    watch(() => props.modelValue, () => {
        if (popoverOpen.value) {
            nextTick(() => {
                scrollToActiveItem();
            });
        }
    });

    return {
      popoverOpen,
      searchText,
      listWrapperRef,
      datasource,
      handleItemSelect,
      startScrolling,
      stopScrolling,
      isAtTop,
      isAtBottom,
      pageUp,
      pageDown,
      pageUpDisabled,
      pageDownDisabled,
      showPageBar,
      activeItemRef,
      loading
    };
  }
};
</script>

<style>
.custom-select-popover .ant-popover-inner {
    padding: 0;
    border-radius: 12px;
}
.custom-select-popover .ant-popover-inner-content {
    padding: 0 !important;
}
.custom-select-popover .ant-popover-arrow {
    display: none;
}
.custom-select-popover .popover-content {
    display: flex;
    flex-direction: row;
    border: 1px solid #C6D2E0;
    border-radius: 10px;
    background: #fff;
    padding: 15px;
}
.custom-select-popover .list-container {
    display: flex;
    flex-direction: column;
    width: 100%;
}
.custom-select-popover .search-wrapper {
    padding-bottom: 10px;
}
.custom-select-popover .search-input {
    width: 100%;
    height: 50px;
    padding: 0 12px;
    border: 1px solid #C6D2E0;
    border-radius: 8px;
    outline: none;
    font-size: 20px;
}
.custom-select-popover .search-input::placeholder {
    color: #A1B1C5;
}
.custom-select-popover .list-wrapper {
    flex-grow: 1;
    overflow-y: auto;
    border: 1px solid #C6D2E0;
    border-radius: 8px;
    scrollbar-width: none;
    -ms-overflow-style: none;
}
.custom-select-popover .list-wrapper::-webkit-scrollbar {
    display: none;
}
.custom-select-popover .list-item {
    padding: 10px 12px;
    cursor: pointer;
    font-size: 22px;
    color: #666666;
    text-align: center;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.3s;
}
.custom-select-popover .list-item:last-child {
    border-bottom: none;
}
.custom-select-popover .list-item.active {
    background-color: #3173C6;
    color: white;
}
.custom-select-popover .list-empty {
    text-align: center;
    color: #999;
    padding: 20px;
    font-size: 20px;
}
.custom-select-popover .list-loading {
    text-align: center;
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.custom-select-popover .popover-content .pageBar {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 30px;
    padding-left: 15px;
}
.custom-select-popover .popover-content .pageBar img {
    cursor: pointer;
    width: 40px;
    height: 40px;
}
</style> 