<template>
    <div>
        <Container>
            <template #head>
                <div class="ip_head">民事一审
                    <Steps :active="4" />
                </div>

            </template>
            <div class="con">
                {{ $store.getters.lafsName }}-{{ $store.getters.applyCaseTypeName}}
                <div class="border" ref="border">
                    <div class="form">
                        <div class="form_head center">
                            基本信息
                        </div>
                        <div class="form_body" v-if="formData.layy">
                            <FormItem label="立案法院">
                                {{formData.layy.fymc}}
                            </FormItem>
                            <FormItem label="案件类型">
                                {{formData.layy.cajlx}}
                            </FormItem>
                            <FormItem label="标的金额(元)">
                                {{formData.layy.sqbdje}}
                            </FormItem>
                            <FormItem label="立案案由" :span="2">
                                {{formData.layy.laay}}
                            </FormItem>
                        </div>

                    </div>
                    <template v-if="dsrs && dsrs.length > 0">
                        <div class="form" v-for="item of dsrs" :key="item">
                            <div class="form_head center">
                                {{ssdws[item.ssdw]}}({{item.cdsrlx}})
                            </div>
                            <div class="form_body" v-if="item.cdsrlx == '自然人'">
                                <FormItem label="当事人类型">
                                    {{item.cdsrlx}}
                                </FormItem>
                                <FormItem label="姓名">
                                    {{item.xm}}
                                </FormItem>
                                <FormItem label="性别">
                                    {{item.cxb}}
                                </FormItem>
                                <FormItem label="国别或地区">
                                    {{item.cgj}}
                                </FormItem>
                                <FormItem label="证件类型">
                                    {{item.czjlx}}
                                </FormItem>
                                <FormItem label="证件号码">
                                    {{item.zjhm}}
                                </FormItem>
                                <FormItem label="出生日期">
                                    {{item.ccsrq}}
                                </FormItem>
                                <FormItem label="年龄">
                                    {{item.nl}}
                                </FormItem>
                                <FormItem label="民族">
                                    {{item.cmz}}
                                </FormItem>
                                <FormItem label="职业">
                                    {{item.zyMc}}
                                </FormItem>
                                <FormItem label="工作单位" :span="2">
                                    {{item.gzdw}}
                                </FormItem>
                                <FormItem label="手机号码">
                                    {{item.sjhm}}
                                </FormItem>
                                <FormItem label="住所地址" :span="2">
                                    {{item.dz}}
                                </FormItem>

                            </div>
                            <div class="form_body" v-else-if="item.cdsrlx == '法人'">
                                <FormItem label="单位名称">
                                    {{item.dwmc}}
                                </FormItem>
                                <FormItem label="单位所在地" :span="2">
                                    {{item.dwzsd}}
                                </FormItem>
                                <FormItem label="国别或地区">
                                    {{item.cgj}}
                                </FormItem>
                                <FormItem label="证照类型">
                                    {{item.czzlx}}
                                </FormItem>
                                <FormItem label="证照号码">
                                    {{item.zzhm}}
                                </FormItem>
                                <FormItem label="法人代表姓名">
                                    {{item.fddbrxm}}
                                </FormItem>
                                <FormItem label="法人代表职务">
                                    {{item.fddbrzw}}
                                </FormItem>
                                <FormItem label="法人证件类型">
                                    {{item.cfddbrzjlx}}
                                </FormItem>
                                <FormItem label="法人证件号码">
                                    {{item.fddbrzjhm}}
                                </FormItem>
                                <FormItem label="法人手机号码">
                                    {{item.fddbrsjhm}}
                                </FormItem>
                                <FormItem label="法人固定电话">
                                    {{item.fddbrgddh}}
                                </FormItem>
                                <FormItem label="单位性质">
                                    {{item.dwxzMc}}
                                </FormItem>

                            </div>
                            <div class="form_body" v-else-if="item.cdsrlx == '非法人组织'">
                                <FormItem label="单位名称">
                                    {{item.dwmc}}
                                </FormItem>
                                <FormItem label="单位所在地" :span="2">
                                    {{item.dwzsd}}
                                </FormItem>
                                <FormItem label="国别或地区">
                                    {{item.cgj}}
                                </FormItem>
                                <FormItem label="证照类型">
                                    {{item.czzlx}}
                                </FormItem>
                                <FormItem label="证照号码">
                                    {{item.zzhm}}
                                </FormItem>
                                <FormItem label="主要负责人姓名">
                                    {{item.fddbrxm}}
                                </FormItem>
                                <FormItem label="主要负责人职务">
                                    {{item.fddbrzw}}
                                </FormItem>
                                <FormItem label="负责人证件类型">
                                    {{item.cfddbrzjlx}}
                                </FormItem>
                                <FormItem label="负责人证件号">
                                    {{item.fddbrzjhm}}
                                </FormItem>
                                <FormItem label="负责人手机号">
                                    {{item.fddbrsjhm}}
                                </FormItem>
                                <FormItem label="负责人固定电话">
                                    {{item.fddbrgddh}}
                                </FormItem>
                                <FormItem label="单位性质">
                                    {{item.dwxzMc}}
                                </FormItem>
                            </div>
                        </div>
                    </template>
                    <template v-if="dlrs && dlrs.length > 0">
                        <div class="form" v-for="item of dlrs" :key="item">
                            <div class="form_head center">
                                代理人信息
                            </div>
                            <div class="form_body">
                                <FormItem label="被代理人" :required="true">
                                    {{item.bdlrMc}}
                                </FormItem>
                                <FormItem label="代理人类型" :required="true" :span="2">
                                    {{item.cdlrlx}}
                                </FormItem>
                                <FormItem label="代理人姓名" :required="true">
                                    {{item.xm}}
                                </FormItem>
                                <FormItem label="代理人证件类型" :required="true">
                                    {{item.czjlx}}
                                </FormItem>
                                <FormItem label="代理人证件号" :required="true">
                                    {{item.zjhm}}
                                </FormItem>
                                <FormItem label="代理人执业证号" :required="true"
                                    v-if="item.dlrlx == '1501_000013-1' || item.dlrlx == '1501_000013-2'">
                                    {{item.zyzh}}
                                </FormItem>
                                <FormItem label="代理人执业机构"
                                    v-if="item.dlrlx == '1501_000013-1' || item.dlrlx == '1501_000013-2'">
                                    {{item.zyjg}}
                                </FormItem>
                                <FormItem label="代理人手机号" :required="true">
                                    {{item.sjhm}}
                                </FormItem>

                            </div>
                        </div>
                    </template>
                    <div class="form">
                        <div class="form_head center">
                            材料信息
                        </div>
                        <div class="form_body pic_list">
                            <div class="pic_item" v-for="sscl of sortedSscls" :key="sscl">
                                {{sscl.clmc}}
                                <img src="@/assets/pageUp.png" @click="scroll(`pic_${sscl.clmc}`,-1)" />
                                <div class="pic_con" :ref="`pic_${sscl.clmc}`">
                                    <img :src="wj.url" v-for="wj of sscl.wjs" :key="wj" />
                                </div>
                                <img src="@/assets/pageDown.png" @click="scroll(`pic_${sscl.clmc}`,1)" />
                            </div>
                            <div class="pic_item" style="height: 0px" v-for="item in 4" :key="item">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="pageBar">
                    <img src="@/assets/pageUp.png" @click="scroll('border',-1)" />
                    <img src="@/assets/pageDown.png" @click="scroll('border',1)" />
                </div>
                <div class="btns">
                    <div class="ip_btn" @click="goBack">上一步</div>
                    <div class="ip_btn deep_btn" @click="goNext">下一步</div>
                </div>
            </div>
        </Container>
    </div>
</template>

<script>
import Container from '@/components/Container.vue'
import Steps from '@/components/Steps.vue'
import FormItem from '@/components/FormItem.vue'
import * as pdfjsLib from 'pdfjs-dist'
export default {
    components: {
        Container,
        Steps,
        FormItem,
    },
    data() {
        return {
            layyid: this.$store.getters.layyid,
            formData: {},
            ssdws: {
                '1501_030109-1': '原告',
                '1501_030109-2': '被告',
                '1501_030109-5': '第三人',
            },
        }
    },
    computed: {
        dsrs() {
            if (!this.formData.dsrs) {
                return []
            }
            return this.formData.dsrs
        },
        dlrs() {
            if (!this.formData.dlrs) {
                return []
            }
            return this.formData.dlrs
        },
        sscls() {
            if (!this.formData.sscls) {
                return []
            }
            let clmcs = this.formData.sscls.map((cl) => cl.clmc)
            let list = this.formData.sscls
                .filter((cl) => clmcs.includes(cl.clmc))
                .sort((a, b) => clmcs.indexOf(a.clmc) - clmcs.indexOf(b.clmc))
            for (let sscl of list) {
                if (!sscl.wjs || sscl.wjs.length == 0) {
                    continue
                }
                for (let item of sscl.wjs) {
                    if (item.extname.toLowerCase() == 'pdf') {
                        this.convertPdfUrlToBase64Image(item.url).then(
                            (res) => {
                                item.url = res
                                item.extname = 'png'
                            }
                        )
                    }
                }
            }

            return list
        },
        // 排序后的列表
        sortedSscls() {
            // 定义优先级顺序
            const priorityOrder = [
                '起诉状',
                '收款账户确认书',
                '当事人身份证明',
                '委托代理人委托手续和身份材料',
                '证据目录及证据材料',
                '送达地址确认书',
                '其他材料',
            ]

            // 返回排序后的数组
            return [...this.sscls].sort((a, b) => {
                const indexA = priorityOrder.indexOf(a.clmc)
                const indexB = priorityOrder.indexOf(b.clmc)

                // 如果都在优先级列表中，按优先级顺序排序
                if (indexA !== -1 && indexB !== -1) {
                    return indexA - indexB
                }
                // 如果a在列表中而b不在，a排在前面
                if (indexA !== -1) {
                    return -1
                }
                // 如果b在列表中而a不在，b排在前面
                if (indexB !== -1) {
                    return 1
                }
                // 如果都不在列表中，保持原有顺序
                return 0
            })
        },
    },
    mounted() {
        pdfjsLib.GlobalWorkerOptions.workerSrc = new URL(
            `../../utils/pdf.worker.min.js`,
            import.meta.url
        ).toString()
        this.getBaseInfo()
    },
    methods: {
        async getBaseInfo() {
            let res = await this.$rpa.dealFetch(
                `https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/layyxq/${this.layyid}/0`,
                {
                    method: 'GET',
                }
            )
            this.formData = res.data
        },
        goBack() {
            this.$router.go(-1)
        },
        async goNext() {
            try {
                await this.$messageConfirm({
                    title: '是否同意先行调解',
                    message:
                        '<p>调解作为非诉讼纠纷解决方式，能及时、高效、低成本、不伤和气地解决纠纷。立案后选择委托调解的，可以快速启动先行调解程序。如不同意调解，法院将尽快安排开庭审理案件，但可能需要经过较长一段时间的排期等待，且审理、执行周期相对较长。</p><p>依照法律规定，调解具有保密性要求，调解过程不公开，调解协议未经当事人同意不得公开。选择委托调解，调解成功且自动履行的免交诉讼费用，申请司法确认的不交纳诉讼费用，要求出具调解书的减半交纳诉讼费用。调解达成的协议具有法律效力，可以依照法律规定申请司法确认，具有强制执行效力。</p><p>首次调解不成功，但仍有继续调解意愿的，可以选择更换调解组织或调解员再进行调解。调解无法达成一致意见的，法院将依程序排期开庭。</p>',
                    confirmText: '同意',
                    cancelText: '不同意',
                    showCancel: true,
                    width: 880,
                    modalClass: 'tiaojie',
                })
            } catch (error) {
                this.$router.push('/Step6')
            }
            this.$router.push('/Step6')
        },
        async convertPdfUrlToBase64Image(url) {
            const loadingTask = pdfjsLib.getDocument(url)
            const pdf = await loadingTask.promise
            const page = await pdf.getPage(1)
            const viewport = page.getViewport({ scale: 2.0 })

            const canvas = document.createElement('canvas')
            const context = canvas.getContext('2d')
            canvas.height = viewport.height
            canvas.width = viewport.width

            const renderContext = {
                canvasContext: context,
                viewport: viewport,
            }

            await page.render(renderContext).promise

            return canvas.toDataURL('image/png')
        },
        scroll(ref, count) {
            let dom = this.$refs[ref]
            if (dom[0]) {
                dom = dom[0]
            }
            dom.scrollBy({
                top: dom.offsetHeight * 0.5 * count,
                behavior: 'smooth',
            })
        },
        getText(value, codes) {
            try {
                return codes.find((t) => t.value == value).text
            } catch (error) {
                return ''
            }
        },
    },
}
</script>
<style scoped>
/deep/ .dq_content {
    text-indent: 10px;
    display: flex;
    align-items: center;
}
.form {
    width: 1660px;
    border: 1px solid #a1b1c5;
    border-radius: 10px;
    overflow: hidden;
    margin-right: 80px;
    margin-bottom: 20px;
}

.form_head {
    background: #f1f5ff;
    border-radius: 10px 10px 0px 0px;
    border-bottom: 1px solid #a1b1c5;
    height: 72px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 32px;
    color: #333333;
}
.form_body {
    padding: 20px;
    display: flex;
    width: 100%;
    flex-wrap: wrap;
}

.pic_list {
    justify-content: space-around;
}

.pic_item {
    width: 320px;
    height: 486px;
    margin: 20px 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 24px;
    white-space: nowrap;
    color: #333333;
}

.pic_item > img {
    margin: 10px;
}

.pic_con::-webkit-scrollbar {
    display: none;
}

.pic_con {
    width: 320px;
    height: 343px;
    display: flex;
    flex-wrap: wrap;
    padding: 10px;
    border: 1px solid #a1b1c5;
    overflow: auto;
}

.pic_con > img {
    flex: none;
    width: 132px;
    height: 162px;
    margin: 8px;
    border: 1px solid #a1b1c5;
}

.pageBar {
    position: absolute;
    height: 100%;
    width: 40px;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    right: 20px;
}
</style>
<style scoped>
.con {
    display: flex;
    align-items: center;
    height: 100%;
    flex-direction: column;
    padding: 20px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 30px;
    color: #333333;
    font-weight: bold;
    position: relative;
}

.border {
    width: 100%;
    height: 564px;
    background: #ffffff;
    margin-top: 20px;
    display: flex;
    align-items: center;
    flex-direction: column;
    overflow: auto;
}
.border::-webkit-scrollbar {
    display: none;
}
.border > * {
    flex: none;
}
.ip_head {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 32px;
    color: #333333;
}

.btns {
    display: flex;
    justify-content: center;
    width: 100%;
    position: relative;
}

.ip_btn {
    width: 216px;
    height: 72px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #3173c6;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #3173c6;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20px 12px 0 12px;
}

.deep_btn {
    background: #3173c6;
    color: #ffffff;
}
</style>
<style>
.tiaojie .message-container {
    white-space: break-spaces;
    text-indent: 48px;
    text-align: left;
}
.tiaojie .message-container p {
    margin-bottom: 0 !important;
}
</style>