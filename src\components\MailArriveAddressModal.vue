<template>
    <div>
        <a-modal :open="open" title="邮寄送达地址" :footer="null" :closable="false" width="1087px" :centered="true"
            wrapClassName="mail-arrive-dialog" :maskClosable="false">
            <div class="modal_content">
                <div class="arrive-address-form">
                    <FormItem label="姓名" :required="true">
                        <input v-model="form.name" placeholder="请输入" />
                    </FormItem>
                    <FormItem label="手机号码" :required="true">
                        <input v-model="form.sjhm" placeholder="请输入" />
                    </FormItem>
                    <FormItem label="省市区" :required="true" :span="2">
                        <div class="address-select-group">
                            <CustomSelect v-model="selectedProvince" :list="provinceList" :width="350" filterable>
                                <div class="selectDiv center" style="border-right: 1px solid #a1b1c5;width: 33%;">
                                    {{ provinceText }}
                                </div>
                            </CustomSelect>
                            <CustomSelect v-if="selectedProvince && cityList.length > 0" v-model="selectedCity" :list="cityList" :width="350" filterable>
                                <div class="selectDiv center" style="border-right: 1px solid #a1b1c5;width: 33%;">
                                    {{ cityText }}
                                </div>
                            </CustomSelect>
                            <CustomSelect v-if="selectedCity && districtList.length > 0" v-model="selectedDistrict" :list="districtList" :width="350" filterable>
                                <div class="selectDiv center" style="flex: 1;">
                                    {{ districtText }}
                                </div>
                            </CustomSelect>
                        </div>
                    </FormItem>
                    <FormItem label="详细地址" :required="true" :span="2" formClass="address-detail">
                        <textarea v-model="form.xxdz" placeholder="请输入详细地址" class="full-width-input"/>
                    </FormItem>
                </div>
            </div>
            <div class="modal_footer">
                <div class="modal_btn normal" @click="$emit('update:open', false)">
                    取消
                </div>
                <div class="modal_btn" @click="submit">
                    提交
                </div>
            </div>
        </a-modal>
    </div>
</template>

<script>
import FormItem from '@/components/FormItem.vue'
import CustomSelect from '@/components/CustomSelect.vue'
export default {
    components: {
        FormItem,
        CustomSelect,
    },
    props: {
        open: {
            type: Boolean,
            required: true,
        },
    },
    created() {
        this.initAreaData()
    },
    data() {
        return {
            form: {},

            areaData: [],
            selectedProvince: '',
            selectedCity: '',
            selectedDistrict: '',
        }
    },
    computed: {
        provinceList() {
            return this.areaData;
        },
        cityList() {
            if (!this.selectedProvince) return [];
            const province = this.areaData.find(p => p.value === this.selectedProvince);
            if (!province || !province.children || province.children.length === 0) return [];
            
            const firstChild = province.children[0];
            if (!firstChild.children || firstChild.children.length === 0) {
                return [{
                    text: '市辖区',
                    value: province.value + '_city_proper',
                    children: province.children
                }];
            } else {
                return province.children;
            }
        },
        districtList() {
            if (!this.selectedCity) return [];
            const city = this.cityList.find(c => c.value === this.selectedCity);

            if (!city || !city.children) return [];
            return city.children;
        },
        provinceText() {
            if (!this.selectedProvince) return '选择省';
            const province = this.provinceList.find(p => p.value === this.selectedProvince);
            return province ? province.text : '选择省';
        },
        cityText() {
            if (!this.selectedCity) return '选择市';
            const city = this.cityList.find(c => c.value === this.selectedCity);
            return city ? city.text : '选择市';
        },
        districtText() {
            if (!this.selectedDistrict) return '选择区';
            const district = this.districtList.find(d => d.value === this.selectedDistrict);
            return district ? district.text : '选择区';
        }
    },
    watch: {
        open(newVal) {
            if (newVal) {
                this.form.name = localStorage.getItem('username')
            }
        },
        selectedProvince(newValue, oldValue) {
            if (newValue !== oldValue) {
                this.selectedCity = '';
                this.selectedDistrict = '';
            }
        },
        selectedCity(newValue, oldValue) {
            if (newValue !== oldValue) {
                this.selectedDistrict = '';
            }
        },
        selectedDistrict(newValue) {
            if (newValue) {
                const province = this.provinceList.find(p => p.value === this.selectedProvince);
                const city = this.cityList.find(c => c.value === this.selectedCity);
                const district = this.districtList.find(d => d.value === this.selectedDistrict);
                if (province && city && district) {
                    this.form.dzMc = province.text + city.text + district.text;
                }
            }
        }
    },
    methods: {
        submit() {
            // 验证手机号码格式
            const phoneRegex = /^\d{11}$/;
            if (!this.form.sjhm || !phoneRegex.test(this.form.sjhm)) {
                this.$message.error('请输入11位手机号码');
                return;
            }
            
            // 验证省市区是否选择完整
            if (!this.selectedProvince) {
                this.$message.error('请选择省');
                return;
            }
            if (this.cityList.length > 0 && !this.selectedCity) {
                this.$message.error('请选择市');
                return;
            }
            if (this.districtList.length > 0 && !this.selectedDistrict) {
                this.$message.error('请选择区');
                return;
            }
            
            // 验证详细地址是否填写
            if (!this.form.xxdz || !this.form.xxdz.trim()) {
                this.$message.error('请输入详细地址');
                return;
            }
            
            let dzMc_parts = [];
            let dz_value = '';
            const province = this.provinceList.find(p => p.value === this.selectedProvince);
            if (province) {
                dzMc_parts.push(province.text);
                dz_value = province.value;
            }
            if (this.selectedCity) {
                const city = this.cityList.find(c => c.value === this.selectedCity);
                if (city) {
                    dzMc_parts.push(city.text);
                    dz_value = city.value;
                }
            }
            if (this.selectedDistrict) {
                const district = this.districtList.find(d => d.value === this.selectedDistrict);
                if (district) {
                    dzMc_parts.push(district.text);
                    dz_value = district.value;
                }
            }
            this.form.dzMc = dzMc_parts.join('');
            this.form.dz = dz_value;
            this.$rpa.dealFetch(
                'https://zxfw.court.gov.cn/yzw/yzw-zxfw-yhfw/api/v1/grxx/yjsd/add',
                { 
                    method: 'POST',
                    body: this.form,
                }
            ).then((res) => {
                if(res.code == 200) {
                    this.$message.success('提交成功')
                    this.$emit('updateList')
                    this.$emit('update:open', false);
                }
            })

            this.$emit('update:open', false);
        },
        initAreaData() {
            this.$rpa.dealFetch(
                'https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/xzqh',
                { method: 'GET' }
            ).then((res) => {
                this.areaData = res.data
            })
        }
    },
}
</script>


<style scoped>
.arrive-address-form {
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
}
.address-select-group {
    display: flex;
    height: 100%;
}
.address-select-item {
    width: 200px;
    height: 56px;
    background: #F2F5F8;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    color: #6E7079;
    cursor: pointer;
}
.full-width-input {
    width: 100%;
    height: 100%;
    padding: 10px 20px;
    box-sizing: border-box;
    resize: none;
    font-size: 22px;
}

.address-detail {
    height: 140px;
}

.address-detail /deep/.dq_label {
    height: 100%;
}

.address-detail /deep/ .dq_content {
    height: 100%;
}

</style>


<style>
.mail-arrive-dialog textarea {
    border: none;
    resize: none;
    outline:none;
}

.mail-arrive-dialog .ant-modal-content {
    padding: 0;
    border-radius: 20px;
    overflow: hidden;
}

.mail-arrive-dialog .ant-modal-title {
    height: 100px;
    background: #dbe6f1;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 42px;
    color: #03206a;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mail-arrive-dialog .modal_content {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 24px;
    color: #333333;
    padding: 20px 40px;
    border-bottom: 2px solid #dbe6f1;
    height: 670px;
}

.mail-arrive-dialog .modal_footer {
    height: 112px;
    padding: 0 50px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.mail-arrive-dialog .modal_btn {
    width: 216px;
    height: 72px;
    background: #3173C6;
    border-radius: 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20px;
    cursor: pointer;
    border: none;
}
.mail-arrive-dialog .modal_btn.normal {
    background: #fff;
    color: #3173C6;
    border: 1px solid #3173C6;
}
</style>