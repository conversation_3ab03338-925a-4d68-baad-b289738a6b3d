<template>
    <div>
        <Container>
            <template #head>
                <div class="ip_head">民事一审
                    <Steps :active="5" />
                </div>

            </template>
            <div class="con">
                {{ $store.getters.lafsName }}-{{ $store.getters.applyCaseTypeName}}
                <div class="border">
                    <img src="@/assets/success.png" alt="" style="margin-bottom: 40px;margin-left: 116px" />
                    请您取走回执，耐心等待法院的审核结果，法院有结果后会通过注册的手机号短信通知您
                </div>
                <div class="btns">
                    <div class="ip_btn deep_btn" @click="goHome">返回首页</div>
                </div>
                <PrintArea style="position: absolute;bottom: 0px" v-show="true" ref="printArea"
                    v-if="$store.getters.layyInfo" />
            </div>
        </Container>
    </div>
</template>

<script>
import Container from '@/components/Container.vue'
import Steps from '@/components/Steps.vue'
import PrintArea from '@/components/PrintArea.vue'
import { DQThermalPrinter } from 'dq-eqc'
import html2canvas from 'html2canvas'
export default {
    components: {
        Container,
        Steps,
        PrintArea,
    },
    data() {
        return {}
    },
    async mounted() {
        let layyInfoRes = await this.$rpa.dealFetch(
            `https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/layyxq/${this.$store.getters.layyId}/0`,
            { method: 'GET' }
        )

        if (layyInfoRes.code === 200 && layyInfoRes.data) {
            // 将立案预约信息存入store
            this.$store.dispatch('layy/setLayyInfo', layyInfoRes.data)
        }

        await this.$nextTick()
        this.htmlToBase64(this.$refs.printArea.$el).then((base64) => {
            DQThermalPrinter.open(
                {
                    printType: DQThermalPrinter.printType.IMAGE,
                    base64Data: base64,
                },
                () => {}
            )
        })
    },
    methods: {
        goHome() {
            this.$router.push('/')
        },
        async htmlToBase64(element) {
            try {
                const canvas = await html2canvas(element, {
                    scale: 1, // 提高分辨率
                    logging: false,
                    useCORS: true, // 允许跨域图片
                    backgroundColor: '#ffffff',
                })

                // 获取Base64编码的PNG图片数据
                const base64Image = canvas.toDataURL('image/png')
                return base64Image
            } catch (error) {
                console.error('转换失败:', error)
                return null
            }
        },
    },
}
</script>

<style scoped>
.con {
    display: flex;
    align-items: center;
    height: 100%;
    flex-direction: column;
    padding: 20px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 30px;
    color: #333333;
    font-weight: bold;
}

.border {
    width: 100%;
    height: 564px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #a1b1c5;
    margin-top: 20px;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 20px 30px;
}

.ip_head {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 32px;
    color: #333333;
}

.btns {
    display: flex;
    justify-content: center;
    width: 100%;
    position: relative;
}

.ip_btn {
    width: 216px;
    height: 72px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #3173c6;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #3173c6;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20px 12px 0 12px;
}

.deep_btn {
    background: #3173c6;
    color: #ffffff;
}
</style>