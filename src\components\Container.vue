<template>
    <div class="container">
        <div class="con_head">
            <slot name="head"></slot>
            <div class="timeout" v-if="timeout && time <= 60">
                <img src="@/assets/container/time.png" />
                {{time}}S
            </div>
        </div>
        <div class="con_body">
            <slot></slot>
        </div>
    </div>
</template>

<script>
export default {
    name: 'Container',
    props: {
        timeout: {
            type: Number,
            default: 120,
        },
        onTimeout: {
            type: Function,
            default: null,
        },
    },
    data() {
        return {
            timer: null,
            time: this.timeout,
        }
    },
    watch: {
        timeout() {
            this.time = this.timeout
        },
    },
    mounted() {
        this.initTimer()
        this.setupInactivityTimer()
    },
    beforeUnmount() {
        if (this.timer) {
            clearTimeout(this.timer)
        }
        document.removeEventListener('click', this.resetInactivityTimer)
        document.removeEventListener('touchstart', this.resetInactivityTimer)

        // 监听滑动事件（触摸屏滑动）
        document.removeEventListener('touchmove', this.resetInactivityTimer)

        // 监听键盘输入
        document.removeEventListener('keydown', this.resetInactivityTimer)
    },
    methods: {
        initTimer() {
            this.timer = setInterval(() => {
                this.time--
                if (this.timeout <= 0) {
                    return
                }
                if (this.time <= 0) {
                    if (this.onTimeout) {
                        this.onTimeout()
                    } else {
                        this.$router.push('/')
                    }
                }
            }, 1000)
        },
        // 初始化事件监听
        setupInactivityTimer() {
            // 监听点击事件（包括触摸屏点击）
            document.addEventListener('click', this.resetInactivityTimer)
            document.addEventListener('touchstart', this.resetInactivityTimer)

            // 监听滑动事件（触摸屏滑动）
            document.addEventListener('touchmove', this.resetInactivityTimer)

            // 监听键盘输入
            document.addEventListener('keydown', this.resetInactivityTimer)
        },
        resetInactivityTimer() {
            this.time = this.timeout
        },
    },
}
</script>

<style scoped>
.container {
    width: calc(100% - 100px);
    height: calc(100% - 90px);
    background-color: #dbe6f1;
    border-radius: 20px;
    padding: 0 20px 50px 20px;
    margin: 20px 50px;
}
.con_head {
    height: 100px;
    width: 100%;
    position: relative;
}
.timeout {
    width: 128px;
    height: 53px;
    padding: 0 17px;
    background: rgba(49, 115, 198, 0.2);
    border-radius: 32px 32px 32px 32px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 26px;
    color: #3173c6;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
}
.con_body {
    height: 746px;
    background: #fff;
    border-radius: 20px;
}
</style>