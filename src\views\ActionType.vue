<template>
    <div class="action-con">
        <Container>
            <div class="con">
                <img src="@/assets/actionType/newCase.png" @click="goChoose" />
                <img src="@/assets/actionType/myCase.png" @click="goMyCase"/>
            </div>
        </Container>
    </div>
</template>

<script>
import Container from '@/components/Container.vue'
export default {
    name: 'ActionType',
    components: {
        Container,
    },
    methods: {
        goChoose() {
            this.$router.push({
                path: '/caseType',
                query: {
                    num: 1
                }
            })
        },
        goMyCase() {
            this.$router.push('/myCaseList')
        }
    },
}
</script>

<style scoped>
.con {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}
.con img {
    margin: 50px;
}
</style>