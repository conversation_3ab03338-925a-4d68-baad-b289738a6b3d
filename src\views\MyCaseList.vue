<template>
    <div class="my-case-list">
        <Container>
            <template #head>
                <div class="user_head">我的立案</div>
            </template>
            <div class="content">
                <div class="filters">
                    <div class="tabs">
                        <div class="tab-item" :class="{ active: activeTab === 'tj' }" @click="activeTab = 'tj'">调解</div>
                        <div class="tab-item" :class="{ active: activeTab === 'sp' }" @click="activeTab = 'sp'">审判</div>
                    </div>
                    <div class="filter-item-wrapper">
                        <div class="filter-item">省份:
                            <span :class="{ active: tempFilters.province !== '' }">
                                {{ tempFilters.province === '' ? '全部' : 
                                tempFilters.province === '0' ? '最高' : 
                                getProvinceText(tempFilters.province) }}
                            </span>
                        </div>
                        <div class="filter-item">时间筛选:
                            <span :class="{ active: tempFilters.time !== '' }">
                                {{ getTimeText(tempFilters.time) }}
                            </span>
                        </div>
                        <div class="filter-item">进展阶段:
                            <span :class="{ active: tempFilters.stage !== '' }">
                                {{ getStageText(tempFilters.stage) }}
                            </span>
                        </div>
                        <div class="filter-item">申请身份:
                            <span :class="{ active: tempFilters.identity !== '' }">
                                {{ getIdentityText(tempFilters.identity) }}
                            </span>
                        </div>
                    </div>
                    <div class="search-bar">
                        <input type="text" placeholder="请输入案件名称查询" v-model="findParam.cxtj" />
                        <button @click="findCaseList">搜索</button>
                    </div>
                    <img src="@/assets/myCaseList/filter.png" alt="筛选" class="filter-icon" @click="openFilterModal" />
                </div>
                <div class="case-container-wrapper">
                    <div ref="caseContainer" class="case-container">
                        <div class="case-list" ref="caseListRef">
                            <div class="case-card" v-for="(item, index) in caseList" :key="index">
                                <div class="card-header">
                                    <div class="case-name">{{ item.name || '名称暂无' }}</div>
                                    <div class="case-type">{{ item.type }}</div>
                                </div>
                                <div class="card-body">
                                    <div class="info-row">
                                        <div class="info-item">
                                            <div class="label">参与人</div>
                                            <div class="value-user">{{ item.participants }}</div>
                                        </div>
                                        <template v-if="activeTab === 'tj'">
                                            <div class="info-item">
                                                <div class="label">申请日期</div>
                                                <div class="value">{{ item.applyDate }}</div>
                                            </div>
                                        </template>
                                        <template v-if="activeTab === 'sp'">
                                            <div class="info-item">
                                                <div class="label">案由</div>
                                                <div class="value">{{ item.reason || '暂无' }}</div>
                                            </div>
                                        </template>
                                    </div>
                                    <div class="info-row">
                                        <template v-if="activeTab === 'sp'">
                                            <div class="info-item">
                                                <div class="label">申请日期</div>
                                                <div class="value">{{ item.applyDate }}</div>
                                            </div>
                                        </template>
                                        <div class="info-item">
                                            <div class="label">法院</div>
                                            <div class="value">{{ item.court }}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="status">{{ item.status }}</div>
                                    <div class="actions">
                                        <img src="@/assets/myCaseList/edit.png" alt="编辑" @click="editCase(item.id)" />
                                        <img src="@/assets/myCaseList/del.png" alt="删除" @click="deleteCase(item.id)" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="pageBar">
                        <img :src="isAtTop ? pageUpDisabled : pageUp" @click="!isAtTop && scrollOnePage(-1)"
                            @touchstart="!isAtTop && scrollOnePage(-1)" />
                        <img :src="isAtBottom && isAllLoaded ? pageDownDisabled : pageDown"
                            @click="!(isAtBottom && isAllLoaded) && scrollOnePage(1)"
                            @touchstart="!(isAtBottom && isAllLoaded) && scrollOnePage(1)" />
                    </div>
                </div>
            </div>
        </Container>
        <a-modal v-model:open="filterModalOpen" title="筛选" :footer="null" :closable="false" width="1580px"
            :centered="true" wrapClassName="filter-dialog" :maskClosable="false">
            <div class="modal_content">
                <div class="filter-item">
                    <div class="filter-item-title">省份</div>
                    <div class="filter-item-content">
                        <div class="filter-item-content-item" :class="{ active: tempFilters.province === '' }"
                            @click="selectProvince('')">全部</div>
                        <div class="filter-item-content-item" :class="{ active: tempFilters.province === '0' }"
                            @click="selectProvince('0')">最高</div>
                        <CustomSelect :list="allProvinceList" :filterable="true" v-model="tempFilters.province"
                            :width="380">
                            <div class="filter-item-content-item" :class="{ active: isProvinceDropdownActive }"
                                @click="handleCustomSelectClick" style="width: 260px; min-width: 260px;">
                                {{ selectedProvinceText }}
                            </div>
                        </CustomSelect>
                    </div>
                </div>
                <div class="filter-item">
                    <div class="filter-item-title">进展阶段</div>
                    <div class="filter-item-content">
                        <div v-for="(item, index) in stageList" :key="index" class="filter-item-content-item"
                            :class="{ active: tempFilters.stage === item.value }"
                            @click="selectFilter('stage', item.value)">
                            {{ item.text }}
                        </div>
                    </div>
                </div>
                <div class="filter-item">
                    <div class="filter-item-title">时间筛选</div>
                    <div class="filter-item-content">
                        <div v-for="(item, index) in timeList" :key="index" class="filter-item-content-item"
                            :class="{ active: tempFilters.time === item.value }"
                            @click="selectFilter('time', item.value)">
                            {{ item.text }}
                        </div>
                    </div>
                </div>
                <div class="filter-item">
                    <div class="filter-item-title">申请身份</div>
                    <div class="filter-item-content">
                        <div v-for="(item, index) in identityList" :key="index" class="filter-item-content-item"
                            :class="{ active: tempFilters.identity === item.value }"
                            @click="selectFilter('identity', item.value)">
                            {{ item.text }}
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal_footer">
                <div class="modal_btn" @click="closeFilterModal"
                    style="background-color: #FFFFFF;color: #3173C6;border: 1px solid #3173C6;">
                    取消
                </div>
                <div class="modal_btn" @click="applyFilter">
                    确定
                </div>
            </div>
        </a-modal>
    </div>
</template>

<script>
import Container from '@/components/Container.vue'
import CustomSelect from '@/components/CustomSelect.vue'
import pageUp from '@/assets/pageUp.png'
import pageDown from '@/assets/pageDown.png'
import pageUpDisabled from '@/assets/pageUp_disable.png'
import pageDownDisabled from '@/assets/pageDown_disable.png'

export default {
    name: 'MyCaseList',
    components: {
        Container,
        CustomSelect,
    },
    data() {
        return {
            filterModalOpen: false,
            activeTab: 'sp',
            scrollStep: 30,
            scrollInterval: null,
            scrollSpeed: 30,
            isAtTop: true,
            isAtBottom: false,
            pageUp,
            pageDown,
            pageUpDisabled,
            pageDownDisabled,
            selectedFilters: {
                province: '',
                stage: '',
                time: '',
                identity: '',
            },
            lastSelectedProvince: '',
            allProvinceList: [
                { text: '北京市', fyid: '1', value: '110000' },
                { text: '天津市', fyid: '51', value: '120000' },
                { text: '河北省', fyid: '100', value: '130000' },
                { text: '山西省', fyid: '300', value: '140000' },
                { text: '内蒙古自治区', fyid: '451', value: '150000' },
                { text: '辽宁省', fyid: '600', value: '210000' },
                { text: '吉林省', fyid: '750', value: '220000' },
                { text: '黑龙江省', fyid: '850', value: '230000' },
                { text: '上海市', fyid: '1100', value: '310000' },
                { text: '江苏省', fyid: '1150', value: '320000' },
                { text: '浙江省', fyid: '1300', value: '330000' },
                { text: '安徽省', fyid: '1451', value: '340000' },
                { text: '福建省', fyid: '1600', value: '350000' },
                { text: '江西省', fyid: '1700', value: '360000' },
                { text: '山东省', fyid: '1850', value: '370000' },
                { text: '河南省', fyid: '2050', value: '410000' },
                { text: '湖北省', fyid: '2250', value: '420000' },
                { text: '湖南省', fyid: '2400', value: '430000' },
                { text: '广东省', fyid: '2550', value: '440000' },
                { text: '广西壮族自治区', fyid: '2750', value: '450000' },
                { text: '海南省', fyid: '2900', value: '460000' },
                { text: '重庆市', fyid: '2950', value: '500000' },
                { text: '四川省', fyid: '3000', value: '510000' },
                { text: '贵州省', fyid: '3250', value: '520000' },
                { text: '云南省', fyid: '3350', value: '530000' },
                { text: '西藏自治区', fyid: '3500', value: '540000' },
                { text: '陕西省', fyid: '3600', value: '610000' },
                { text: '甘肃省', fyid: '3750', value: '620000' },
                { text: '青海省', fyid: '3900', value: '630000' },
                { text: '宁夏回族自治区', fyid: '4000', value: '640000' },
                { text: '新疆维吾尔自治区', fyid: '4050', value: '650000' },
                { text: '新疆生产建设兵团', fyid: '4166', value: '660000' },
            ],
            stageList: [
                { text: '全部', value: '' },
                { text: '待提交', value: '11800007-100' },
                { text: '待审核', value: '11800007-1' },
                { text: '待补充材料', value: '11800007-6' },
                { text: '审核通过', value: '11800007-2' },
                { text: '审核不通过', value: '11800007-3' },
                { text: '已立案', value: '11800007-4' },
                { text: '不予立案', value: '11800007-5' },
            ],
            timeList: [
                { text: '全部', value: '' },
                { text: '近1个月', value: 30 },
                { text: '近3个月', value: 90 },
                { text: '近6个月', value: 180 },
            ],
            identityList: [
                { text: '全部', value: '' },
                { text: '本人申请', value: '11800010-1' },
                { text: '为他人或公司等组织申请', value: '11800010-2' },
            ],
            findParam: {
                // 案件名称
                cxtj: '',
                // 开始时间
                kssj: '',
                // 结束时间
                jssj: '',
                // 状态
                zt: '',
                // 每页数量
                limit: 12,
                // 页码
                page: 1,
                // 省份
                sfid: '',
                // 案件类型
                ajlb: '',
                // 申请人身份
                sqrsf: '',
            },
            caseCount: 0,
            caseList: [],
            tempFilters: {
                province: '',
                stage: '',
                time: '',
                identity: '',
            },
            isLoadingMore: false,
            isAllLoaded: false,
        }
    },
    computed: {
        isProvinceDropdownActive() {
            return (
                this.tempFilters.province !== '' &&
                this.tempFilters.province !== '0'
            )
        },
        selectedProvinceText() {
            if (this.isProvinceDropdownActive) {
                const province = this.allProvinceList.find(
                    (p) => p.value === this.tempFilters.province
                )
                return province ? province.text : '选择'
            }
            return '选择'
        },
    },
    methods: {
        async editCase(layyId) {
            let layyInfoRes = await this.$rpa.dealFetch(
                `https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/layyxq/${layyId}/0`,
                { method: 'GET' }
            )

            if (layyInfoRes.code === 200 && layyInfoRes.data) {
                this.$store.dispatch('layy/setLayyId', layyId)
                this.$store.dispatch('layy/setLayyInfo', layyInfoRes.data)
                let laay = layyInfoRes.data.layy.laay
                let ssclsList = layyInfoRes.data.sscls
                // 如果有案由并且也有材料列表，则跳转文件列表页面
                if(!!laay && ssclsList.some(item => !!item.wjs && item.wjs.length > 0)) {
                    this.$router.push({
                        path: '/step3',
                        query: {
                            id: layyId,
                        },
                    })
                    return
                }

                this.$router.push({
                    path: '/step2',
                    query: {
                        id: layyId,
                    },
                })
            }

        },
        async findCaseList() {
            // 重置分页状态
            this.isAllLoaded = false
            // 重置页码
            this.findParam.page = 1
            let baseUrl =
                'https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy'
            let params = `?cxtj=${encodeURIComponent(
                this.findParam.cxtj
            )}&kssj=${encodeURIComponent(
                this.findParam.kssj
            )}&jssj=${encodeURIComponent(
                this.findParam.jssj
            )}&zt=${encodeURIComponent(this.findParam.zt)}&limit=${
                this.findParam.limit
            }&page=${this.findParam.page}&sfid=${encodeURIComponent(
                this.findParam.sfid
            )}&sqrsf=${encodeURIComponent(
                this.findParam.sqrsf
            )}&ajlb=${encodeURIComponent(this.activeTab)}`
            let countUrl = `${baseUrl}/count${params}`
            let listUrl = `${baseUrl}${params}`
            // 查询案件总数量
            let countRes = await this.$rpa.dealFetch(countUrl, {
                methods: 'GET',
            })
            this.caseCount = countRes.data
            // 查询案件列表
            this.caseList = []
            let res = await this.$rpa.dealFetch(listUrl, { methods: 'GET' })
            if (res.data && res.data.length > 0) {
                res.data.forEach((item) => {
                    this.caseList.push({
                        id: item.id,
                        name: item.ajmc,
                        type: item.cajlx,
                        participants: item.dsrMc,
                        reason: item.laay,
                        applyDate: item.createTime,
                        court: item.fymc,
                        status: this.stageList.find((s) => s.value == item.zt)
                            .text,
                    })
                })
                // 检查是否已加载全部数据
                if (
                    this.caseCount > 0 &&
                    this.caseList.length >= this.caseCount
                ) {
                    this.isAllLoaded = true
                } else {
                }
            }
            this.$nextTick(() => {
                // 重置滚动位置到顶部
                const caseContainer = this.$refs.caseContainer
                if (caseContainer) {
                    caseContainer.scrollTop = 0
                }
                this.checkScrollPosition()
            })
        },
        async deleteCase(caseId) {
            try {
                await this.$messageConfirm({
                    showCancel: true,
                    message: '确定要删除该案件吗？',
                })
                // 用户点击了确认
                let deleteUrl =
                    'https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/' +
                    caseId
                try {
                    await this.$rpa.dealFetch(deleteUrl, { method: 'DELETE' })
                    // 重新加载数据
                    this.findCaseList()
                } catch (error) {
                    console.error('删除案件失败', error)
                }
            } catch (error) {
                // 用户取消了操作
                console.log('用户取消了删除操作')
            }
        },
        getProvinceText(value) {
            if (!value) return '全部'
            if (value === '0') return '最高'
            const province = this.allProvinceList.find(
                (item) => item.value === value
            )
            return province ? province.text : '选择'
        },
        getTimeText(value) {
            if (!value) return '全部'
            const time = this.timeList.find((item) => item.value === value)
            return time ? time.text : '全部'
        },
        getStageText(value) {
            if (!value) return '全部'
            const stage = this.stageList.find((item) => item.value === value)
            return stage ? stage.text : '全部'
        },
        getIdentityText(value) {
            if (!value) return '全部'
            const identity = this.identityList.find(
                (item) => item.value === value
            )
            return identity ? identity.text : '全部'
        },
        selectProvince(value) {
            this.tempFilters.province = value
        },
        handleCustomSelectClick() {
            if (
                this.tempFilters.province === '' ||
                this.tempFilters.province === '0'
            ) {
                if (this.lastSelectedProvince) {
                    this.tempFilters.province = this.lastSelectedProvince
                }
            }
        },
        scrollOnePage(direction) {
            const caseContainer = this.$refs.caseContainer
            if (caseContainer) {
                // 计算滚动距离，为容器高度的一倍
                const scrollDistance = Math.floor(caseContainer.clientHeight)
                // 使用平滑滚动
                caseContainer.scrollBy({
                    top: direction * scrollDistance,
                    behavior: 'smooth',
                })
                // 滚动完成后检查位置
                setTimeout(() => {
                    this.checkScrollPosition()
                }, 500) // 给滚动动画留出足够时间
            }
        },
        checkScrollPosition() {
            const container = this.$refs.caseContainer
            if (container) {
                this.isAtTop = container.scrollTop <= 0

                // 修改判断逻辑，只有在已加载全部数据且滚动到底部时才禁用向下按钮
                // 检查是否滚动到底部
                this.isAtBottom =
                    container.scrollHeight -
                        container.scrollTop -
                        container.clientHeight <=
                    2

                // 只有当没有滚动到底部且没有在加载数据时，才检查是否需要加载更多
                if (!this.isAtBottom && !this.isLoadingMore) {
                    const scrollPercentage =
                        (container.scrollTop + container.clientHeight) /
                        container.scrollHeight
                    if (
                        scrollPercentage > 0.8 &&
                        this.caseList.length >= this.findParam.limit &&
                        !this.isAllLoaded
                    ) {
                        this.loadMoreData()
                    }
                }
            }
        },
        async loadMoreData() {
            if (this.isLoadingMore || this.isAllLoaded) {
                // console.log("跳过加载更多: isLoadingMore=", this.isLoadingMore, "isAllLoaded=", this.isAllLoaded);
                return
            }

            this.isLoadingMore = true
            // 更新滚动状态
            this.checkScrollPosition()

            console.log('加载更多数据, 页码:', this.findParam.page + 1)
            this.findParam.page++

            let baseUrl =
                'https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy'
            let params = `?cxtj=${encodeURIComponent(
                this.findParam.cxtj
            )}&kssj=${encodeURIComponent(
                this.findParam.kssj
            )}&jssj=${encodeURIComponent(
                this.findParam.jssj
            )}&zt=${encodeURIComponent(this.findParam.zt)}&limit=${
                this.findParam.limit
            }&page=${this.findParam.page}&sfid=${encodeURIComponent(
                this.findParam.sfid
            )}&sqrsf=${encodeURIComponent(
                this.findParam.sqrsf
            )}&ajlb=${encodeURIComponent(this.activeTab)}`
            let listUrl = `${baseUrl}${params}`

            try {
                let res = await this.$rpa.dealFetch(listUrl, { methods: 'GET' })
                if (res.data && res.data.length > 0) {
                    console.log('成功加载更多数据:', res.data.length, '条')
                    const newCases = res.data.map((item) => ({
                        id: item.id,
                        name: item.ajmc,
                        type: item.cajlx,
                        participants: item.dsrMc,
                        reason: item.laay,
                        applyDate: item.createTime,
                        court: item.fymc,
                        status:
                            this.stageList.find((s) => s.value == item.zt)
                                ?.text || '未知',
                    }))
                    this.caseList.push(...newCases)

                    // 检查是否已加载全部数据
                    if (
                        this.caseCount > 0 &&
                        this.caseList.length >= this.caseCount
                    ) {
                        this.isAllLoaded = true
                        console.log('已加载全部数据')
                    }
                } else {
                    // 没有更多数据了
                    console.log('没有更多数据可加载')
                    this.isAllLoaded = true
                }
            } catch (error) {
                console.error('加载更多数据失败', error)
            } finally {
                this.isLoadingMore = false
                // 更新滚动状态
                this.$nextTick(() => {
                    this.checkScrollPosition()
                })
            }
        },
        selectFilter(type, value) {
            this.tempFilters[type] = value
        },
        closeFilterModal() {
            this.filterModalOpen = false
        },
        applyFilter() {
            this.caseList = []
            this.selectedFilters = { ...this.tempFilters }
            this.findParam.sfid = this.selectedFilters.province
            this.findParam.ajlb = this.activeTab
            this.findParam.zt = this.selectedFilters.stage
            if (this.selectedFilters.time) {
                // kssj为当前日期减去time的日期，格式为yyyy-MM-dd
                let kssj = new Date()
                kssj.setDate(kssj.getDate() - this.selectedFilters.time)
                this.findParam.kssj = kssj.toISOString().split('T')[0]
                // jssj为当前日期，格式为yyyy-MM-dd
                let jssj = new Date()
                this.findParam.jssj = jssj.toISOString().split('T')[0]
            }
            this.findParam.sqrsf = this.selectedFilters.identity
            this.findParam.page = 1
            this.findCaseList()
            this.closeFilterModal()
            this.$nextTick(() => {
                this.checkScrollPosition()
            })
        },
        openFilterModal() {
            this.tempFilters = { ...this.selectedFilters }
            this.filterModalOpen = true
        },
    },
    watch: {
        'tempFilters.province'(newValue) {
            if (this.allProvinceList.some((p) => p.value === newValue)) {
                this.lastSelectedProvince = newValue
            }
        },
        caseList() {
            this.$nextTick(() => {
                this.checkScrollPosition()
            })
        },
        activeTab() {
            this.caseList = []
            this.findParam.ajlb = this.activeTab
            this.findParam.page = 1
            this.isAllLoaded = false
            this.findCaseList()
            this.$nextTick(() => {
                this.checkScrollPosition()
            })
        },
    },
    mounted() {
        this.tempFilters = { ...this.selectedFilters }
        this.$nextTick(() => {
            this.checkScrollPosition()
            const container = this.$refs.caseContainer
            if (container) {
                container.addEventListener('scroll', this.checkScrollPosition)
            }
            window.addEventListener('resize', this.checkScrollPosition)
        })
        this.findCaseList()
    },
    beforeUnmount() {
        const container = this.$refs.caseContainer
        if (container) {
            container.removeEventListener('scroll', this.checkScrollPosition)
        }
        window.removeEventListener('resize', this.checkScrollPosition)
    },
}
</script>

<style scoped>
.user_head {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 32px;
    color: #333333;
}
.tabs {
    display: flex;
}
.tab-item {
    padding: 8px 24px;
    border: 1px solid #dbe6f1;
    cursor: pointer;
    background-color: #dbe6f1;
    color: #a1b1c5;
    font-size: 24px;
    transition: all 0.3s;
}
.tab-item:first-child {
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
}
.tab-item:last-child {
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    border-left: none;
}
.tab-item.active {
    background-color: #3173c6;
    color: #fff;
    border-color: #3173c6;
    z-index: 1;
}
.content {
    background-color: #fff;
    padding: 20px;
    border-radius: 10px;
}
.filters {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    border-radius: 5px;
}
.filter-item-wrapper {
    display: flex;
    flex: 1;
    justify-content: flex-end;
}
.filter-item {
    margin-right: 40px;
    color: #a1b1c5;
    font-size: 22px;
}
.filter-item span {
    color: #333;
    font-weight: 500;
    font-size: 22px;
    display: inline-block;
    padding-left: 10px;
}
.filter-item span.active {
    color: #0b7cff;
}
.search-bar {
    display: flex;
    margin-left: auto;
}
.search-bar input {
    border: 1px solid #d9d9d9;
    border-right: none;
    padding: 8px 12px;
    border-radius: 10px 0 0 10px;
    outline: none;
    font-size: 24px;
}
.search-bar input::placeholder {
    color: #a1b1c5;
}
.search-bar button {
    background-color: #3173c6;
    color: #fff;
    border: none;
    padding: 8px 15px;
    border-radius: 0 10px 10px 0;
    cursor: pointer;
    font-size: 24px;
}
.filter-icon {
    width: 40px;
    height: 40px;
    margin-left: 15px;
    cursor: pointer;
}
.case-container-wrapper {
    border: 1px solid #dbe6f1;
    border-radius: 10px;
    padding: 20px;
    height: 656px;
    display: flex;
    position: relative;
}
.case-container {
    flex: 1;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}
.case-container::-webkit-scrollbar {
    display: none;
}
.case-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
    gap: 20px;
}
.case-card {
    border: 1px solid #e8e8e8;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    height: 312px;
}
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e8e8e8;
    height: 64px;
    padding: 0 20px;
    background: #f1f5ff;
}
.case-name {
    font-weight: bold;
    font-size: 24px;
    color: #333333;
}
.case-type {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 20px;
    color: #a1b1c5;
}
.card-body {
    flex-grow: 1;
    padding: 20px;
    border-bottom: 1px solid #dbe6f1;
}
.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}
.info-row:last-child {
    margin-bottom: 0px;
}
.info-item:first-child {
    display: flex;
    flex-direction: column;
    width: 40%;
}
.info-item:last-child {
    display: flex;
    flex-direction: column;
    width: 55%;
}
.label {
    color: #999;
    font-size: 20px;
    margin-bottom: 5px;
}
.value-user {
    color: #333;
    font-size: 22px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.value {
    color: #78808a;
    font-size: 22px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
    padding: 0 20px;
}
.status {
    color: #c67b31;
    font-weight: 500;
    font-size: 24px;
}
.actions img {
    width: 40px;
    height: 40px;
    cursor: pointer;
    margin-left: 15px;
}
.pageBar {
    width: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 50px;
    margin-left: 20px;
}
</style>

<style>
.filter-dialog .ant-modal-content {
    padding: 0;
    border-radius: 20px;
    overflow: hidden;
}
.filter-dialog .ant-modal-title {
    height: 100px;
    background: #dbe6f1;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 42px;
    color: #03206a;
    display: flex;
    align-items: center;
    justify-content: center;
}
.filter-dialog .modal_content {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 30px;
    color: #333333;
    padding: 10px 50px;
    border-bottom: 2px solid #dbe6f1;
    display: flex;
    flex-direction: column;
}
.filter-dialog .modal_footer {
    height: 112px;
    padding: 0 50px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}
.filter-dialog .modal_btn {
    width: 216px;
    height: 72px;
    background: #3173c6;
    border-radius: 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20px;
}
.filter-dialog .filter-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 30px;
}
.filter-dialog .filter-item .filter-item-title {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 42px;
    color: #333333;
    text-align: left;
    margin-bottom: 5px;
}
.filter-dialog .filter-item .filter-item-content {
    display: flex;
    flex-wrap: wrap;
}
.filter-dialog .filter-item .filter-item-content .filter-item-content-item {
    padding: 0 20px;
    margin-right: 20px;
    margin-bottom: 10px;
    min-width: 160px;
    height: 60px;
    line-height: 60px;
    background: #f1f5ff;
    border-radius: 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 24px;
    color: #3173c6;
    text-align: center;
    cursor: pointer;
}
.filter-dialog
    .filter-item
    .filter-item-content
    .filter-item-content-item.active {
    background: #3173c6;
    color: #ffffff;
}
</style>