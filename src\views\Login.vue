<template>
    <div class="login_con">
        <div class="left">
            <img src="@/assets/login/device.png" alt="" />
            <div>欢迎使用{{deviceName}}</div>
        </div>
        <div class="right">
            <div class="tabs">
                <div class="tab" :class="{ active: activeTab === '扫码登录' }" @click="switchLoginType('扫码登录')"
                    style="border-radius: 20px 0 0 0 " data-click="扫码登录">扫码登录</div>
                <div class="tab" :class="{ active: activeTab === '密码登录' }" @click="switchLoginType('密码登录')"
                    style="border-radius: 0 20px 0 0 " data-click="密码登录">密码登录</div>
            </div>
            <div class="content">
                <div v-if="activeTab === '扫码登录'" class="scan_login">
                    <img :src="(!qrCode || registerModalOpen) ? tempQrCode : qrCode" alt="" @click="refreshQrCode" />
                    打开微信扫一扫登录
                </div>
                <div v-else-if="activeTab === '密码登录'" class="account_login">
                    <div class="login_input">
                        <img src="@/assets/login/username.png" alt="" />
                        <input v-model="loginName" type="text" placeholder="请输入账号" autocomplete="off" />
                    </div>
                    <div class="login_input">
                        <img src="@/assets/login/password.png" alt="" />
                        <input v-model="password" type="password" placeholder="请输入密码" autocomplete="new-password" />
                    </div>
                    <div class="login_input" style="margin-bottom: 5px;">
                        <img src="@/assets/login/captcha.png" alt="" />
                        <input v-model="captcha" type="text" placeholder="请输入验证码" autocomplete="off" />
                        <img :src="codePic" class="captcha" @click="refreshCaptcha" />
                    </div>
                    <div v-if="passwordError" class="error-message">{{ passwordError }}</div>
                    <div class="login_btn" @click="handleAccountLogin">登录
                    </div>
                </div>
            </div>
            <div class="other">
                <div v-if="activeTab === '扫码登录'">
                    <div class="other_word">还没账号，</div>
                    <div @click="register()" class="other_btn">立即注册</div>
                </div>
                <div v-else-if="activeTab === '密码登录'">
                    <div class="other_word">忘记密码，</div>
                    <div @click="forgetPassword()" class="other_btn">找回密码</div>
                </div>
            </div>
        </div>

        <a-modal v-model:open="registerModalOpen" title="手机扫码" :footer="null" :closable="false" width="500px"
            :centered="true" wrapClassName="register-dialog" :maskClosable="false">
            <div class="modal_content">
                <img :src="registerQrCode" alt="" @click="refreshRegisterQrCode" style="min-height: 272px" />
            </div>
            <div class="modal_footer">
                <div class="modal_btn" @click="closeRegisterModal">
                    关闭
                </div>
            </div>
        </a-modal>

        <a-modal v-model:open="passwordModalOpen" :footer="null" :closable="false" width="690px" :centered="true"
            wrapClassName="password-dialog" :maskClosable="false" :keyboard="false">
            <div class="password-dialog-content">
                <div class="password-dialog-content-inner">
                    <div class="modal_content password-modal-content">
                        <div class="main-title">{{ boundPhoneNumber }}</div>
                        <div class="subtitle">请设定账号密码</div>
                        <div class="rules">密码设置规则为: 8位以上的数字和字母</div>
                        <div class="input-wrapper">
                            <div class="input-container">
                                <input :type="passwordVisible ? 'text' : 'password'" v-model="newPassword"
                                    placeholder="请输入密码" />
                                <span class="toggle-password" @click="togglePasswordVisibility">
                                    <EyeOutlined v-if="passwordVisible" />
                                    <EyeInvisibleOutlined v-else />
                                </span>
                            </div>
                        </div>
                        <div class="input-wrapper">
                            <div class="input-container">
                                <input :type="confirmPasswordVisible ? 'text' : 'password'" v-model="confirmPassword"
                                    placeholder="请输入确认密码" />
                                <span class="toggle-password" @click="toggleConfirmPasswordVisibility">
                                    <EyeOutlined v-if="confirmPasswordVisible" />
                                    <EyeInvisibleOutlined v-else />
                                </span>
                            </div>
                            <div v-if="passwordError" class="error-message">{{ passwordError }}</div>
                        </div>
                        <div class="agreement" v-if="isRegister">
                            <input type="checkbox" v-model="agreedToTerms" id="agreement-checkbox" />
                            <label for="agreement-checkbox">
                                已阅读并同意<a href="#" @click.prevent="agreementModalOpen = true">《注册协议》</a><a href="#"
                                    @click.prevent="privacyPolicyModalOpen = true">《隐私政策》</a>
                            </label>
                        </div>
                    </div>
                    <div class="modal_footer password-modal-footer">
                        <div class="modal_btn" @click="submitNewPassword">
                            {{ isRegister ? '立即提交' : '更新密码' }}
                        </div>
                    </div>
                </div>
            </div>
        </a-modal>

        <!-- 使用封装的协议弹窗组件 -->
        <AgreementModals v-model:agreementModalOpen="agreementModalOpen"
            v-model:privacyPolicyModalOpen="privacyPolicyModalOpen" />

    </div>
</template>
  
<script>
import AgreementModals from '@/components/AgreementModals.vue'
import { EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons-vue'

export default {
    name: 'Login',
    components: {
        AgreementModals,
        EyeOutlined,
        EyeInvisibleOutlined,
    },
    inject: ['courtName', 'deviceName'],
    // 添加计算属性，用于从store获取用户信息
    computed: {
        currentUserInfo() {
            return this.$store.state.user.currentUserInfo
        },
    },
    data() {
        return {
            activeTab: '',
            qrCode: '',
            timeout: null,
            interval: null,

            loginName: '',
            password: '',
            captcha: '', // 验证码输入
            codePic: '',
            waitTime: 1000,
            tempQrCode: require('@/assets/temp_qrcode.png'),

            // 注册弹窗
            registerQrCode: '',
            registerModalOpen: false,
            registerCheckTimeout: null,
            passwordModalOpen: false,
            boundPhoneNumber: '',
            newPassword: '',
            confirmPassword: '',
            passwordError: '', // 密码错误提示信息
            passwordVisible: false, // 控制密码可见性
            confirmPasswordVisible: false, // 控制确认密码可见性
            agreedToTerms: false,
            agreementModalOpen: false,
            privacyPolicyModalOpen: false,

            isRegister: null,
        }
    },
    async mounted() {
        this.switchLoginType('扫码登录')
        // 初始化rpa判断是否存在token
        this.interval = setInterval(async () => {
            let res = await this.$rpa.getLocalStorage({
                keyList: [
                    'zxfwtoken',
                    'username',
                    'userId',
                    'role',
                    'roleText',
                    'dsrlx',
                ],
            })
            if (res.success && res.values) {
                // 将返回的值存入localStorage
                if (res.values.zxfwtoken)
                    localStorage.setItem('zxfwtoken', res.values.zxfwtoken)
                if (res.values.username)
                    localStorage.setItem('username', res.values.username)
                if (res.values.userId)
                    localStorage.setItem('userId', res.values.userId)
                if (res.values.role)
                    localStorage.setItem('role', res.values.role)
                if (res.values.roleText)
                    localStorage.setItem('roleText', res.values.roleText)
                if (res.values.dsrlx)
                    localStorage.setItem('dsrlx', res.values.dsrlx)

                if (!!res.values.zxfwtoken) {
                    let jbxxRes = await this.$rpa.dealFetch(
                        'https://zxfw.court.gov.cn/yzw/yzw-zxfw-yhfw/api/v1/grxx/jbxx',
                        { method: 'GET' }
                    )
                    if (jbxxRes.code == 200) {
                        let jbxx = jbxxRes.data
                        jbxx.userId = res.values.userId
                        // 将用户信息存储到store中
                        this.$store.dispatch('user/setCurrentUserInfo', jbxx)
                        setTimeout(() => {
                            this.$store.dispatch('loading/setLoading', false)
                        }, 300)
                        this.$router.push('/actionType')
                    }
                }
            }
        }, 1000)
    },

    methods: {
        // 根据需求获取qr或者captcha
        getQrOrCaptcha() {
            if (this.activeTab == '密码登录') {
                this.getCaptcha()
            } else {
                this.getQr()
            }
        },
        getQr() {
            this.getImage('fd-login-qrcode', (image) => {
                this.qrCode = image
                if (this.qrCode) {
                    clearTimeout(this.timeout)
                    this.timeout = setTimeout(() => {
                        this.getQr()
                    }, this.waitTime)
                }
            })
        },
        async refreshQrCode() {
            await this.$rpa.operateElement([
                {
                    type: 'click',
                    locateBy: 'className',
                    locateValue: 'fd-login-qrcode-mask',
                },
            ])

            if (this.timeout) {
                clearTimeout(this.timeout)
            }
            this.getQr()
        },
        refreshCaptcha() {
            this.clickButton('className', 'fd-images-code', (res) => {
                if (!res.all_success) {
                    this.clickButton('className', 'fd-reload-captcha')
                }
            })
        },
        async clickButton(locateBy, locateValue, cb) {
            let res = await this.$rpa.operateElement([
                {
                    type: 'click',
                    locateBy: locateBy,
                    locateValue: locateValue,
                },
            ])

            if (cb) cb(res)
        },
        async switchLoginType(type) {
            if (type == this.activeTab) return
            this.activeTab = type
            clearTimeout(this.timeout)
            await this.$rpa.operateElement([
                {
                    type: 'click',
                    locateBy: 'xpath',
                    locateValue: `//uni-view[@class='fd-sub-tab' and text()='${type}']`,
                    force: true,
                },
            ])

            this.getQrOrCaptcha()
        },
        getCaptcha() {
            this.getImage('fd-images-code', (image) => {
                this.codePic = image
                if (image) {
                    this.timeout = setTimeout(() => {
                        this.getCaptcha()
                    }, this.waitTime)
                } else {
                    if (this.activeTab == '密码登录') {
                    }
                }
            })
        },
        async getImage(className, cb) {
            let res = await this.$rpa.getImage({
                locateBy: 'className',
                locateValue: className,
            })
            cb(res.image)
        },

        async handleAccountLogin() {
            // 处理账号登录逻辑
            // 添加登录名密码验证码为空的校验
            if (!this.loginName) {
                this.passwordError = '请输入账号'
                return
            }
            if (!this.password) {
                this.passwordError = '请输入密码'
                return
            }
            if (!this.captcha) {
                this.passwordError = '请输入验证码'
                return
            }
            this.$store.dispatch('loading/setLoading', true)
            this.passwordError = ''
            let elements = [
                {
                    type: 'input',
                    locateBy: 'xpath',
                    locateValue: `//uni-view[contains(@class, 'fd-input-loginid')]//input`,
                    inputText: this.loginName,
                },
                {
                    type: 'input',
                    locateBy: 'xpath',
                    locateValue: `//uni-view[contains(@class, 'fd-input-pwd')]//input`,
                    inputText: this.password,
                },
                {
                    type: 'input',
                    locateBy: 'xpath',
                    locateValue: `//uni-view[contains(@class, 'fd-input-code')]//input`,
                    inputText: this.captcha,
                    delay: 3,
                },
                {
                    type: 'click',
                    locateBy: 'className',
                    locateValue: `fd-login-btn`,
                },
            ]
            let res = await this.$rpa.operateElement(elements)
            if (res.all_success) {
                setTimeout(() => {
                    this.$store.dispatch('loading/setLoading', false)
                }, 300)
                const errorText = await this.getText(
                    '//uni-view[contains(@class, "fd-error-tip")]'
                )
                this.passwordError = errorText
                if (errorText) {
                    setTimeout(() => {
                        this.$store.dispatch('loading/setLoading', false)
                    }, 300)
                }
            }
        },

        register() {
            this.dealRegisterMain(`//uni-text[span[text()='立即注册']]`)
            this.isRegister = true
        },

        forgetPassword() {
            this.dealRegisterMain(`//uni-view[text()='忘记密码']`)
            this.isRegister = false
        },

        // 打开注册弹窗
        async dealRegisterMain(locateValue) {
            this.$store.dispatch('loading/setLoading', true)
            
            let res = {};
            while(!res.all_success) {
                res = await this.$rpa.operateElement([
                    {
                        type: 'click',
                        locateBy: 'xpath',
                        locateValue: locateValue,
                        force: true,
                    },
                ])
                // 添加500ms延迟，避免请求过于频繁
                if (!res.all_success) {
                    await new Promise(resolve => setTimeout(resolve, 500))
                }
            }

            this.getRegisterQr()
            this.qrCode = null
            this.$nextTick(() => {
                this.registerModalOpen = true
            })

            // 启动定期检查注册扫码流程是否结束
            if (this.registerCheckTimeout) {
                clearTimeout(this.registerCheckTimeout)
            }
            this.checkRegisterStatus()
        },

        async checkRegisterStatus() {
            let text = null;
            while (!text) {
                text = await this.getText(
                    '//uni-view[@class="fd-text-subtip" and text()="请设定账号密码"]'
                )
                if (text) {
                    // 立即清除定时器，防止getRegisterQr继续执行
                    if (this.timeout) {
                        clearTimeout(this.timeout)
                    }
                    const phoneText = await this.getText(
                        '//uni-view[contains(@class, "fd-text-toptip")]'
                    )
                    if (phoneText) {
                        this.boundPhoneNumber = phoneText
                    }
                    this.closeRegisterModal()
                    this.passwordError = ''
                    this.passwordModalOpen = true
                    return // Exit the loop and function
                }
                
                // 2000ms后再次检查
                await new Promise(
                    resolve =>
                        (this.registerCheckTimeout = setTimeout(resolve, 1000))
                )
            }
        },

        // 关闭注册弹窗
        async closeRegisterModal() {
            clearTimeout(this.timeout)
            if (this.registerCheckTimeout) {
                clearTimeout(this.registerCheckTimeout)
            }
            this.registerModalOpen = false
            this.registerQrCode = null
            await this.$rpa.operateElement([
                {
                    type: 'click',
                    locateBy: 'xpath',
                    locateValue: `//uni-text[span[text()='返回登录']]`,
                },
            ])
            this.getQrOrCaptcha()
        },

        resetPasswordModal() {
            this.boundPhoneNumber = ''
            this.newPassword = ''
            this.confirmPassword = ''
            this.passwordError = ''
            this.passwordVisible = false
            this.confirmPasswordVisible = false
            this.agreedToTerms = false
        },

        // 获取注册二维码
        getRegisterQr() {
            this.getImage('fd-regist-qrcode', (image) => {
                setTimeout(() => {
                    this.$store.dispatch('loading/setLoading', false)
                }, 300)
                this.registerQrCode = image
                if (this.registerQrCode) {
                    this.timeout = setTimeout(() => {
                        this.getRegisterQr()
                    }, this.waitTime)
                }
            })
        },

        // 刷新注册二维码
        async refreshRegisterQrCode() {
            await this.$rpa.operateElement([
                {
                    type: 'click',
                    locateBy: 'className',
                    locateValue: 'fd-qr-scan',
                },
            ])

            if (this.timeout) {
                clearTimeout(this.timeout)
            }
            this.getRegisterQr()
        },

        // 获取文本
        async getText(xpathValue) {
            let res = await this.$rpa.getText({
                locateBy: 'xpath',
                locateValue: xpathValue,
            })
            return res.text
        },

        async submitNewPassword() {
            // 先验证密码
            this.validatePassword()
            this.validatePasswordConfirm()

            // 如果有错误，不继续提交
            if (this.passwordError) {
                return
            }

            if (!this.agreedToTerms && this.isRegister) {
                this.passwordError = '请阅读并同意注册协议和隐私政策'
                return
            }

            this.$store.dispatch('loading/setLoading', true)
            let elements = [
                {
                    type: 'input',
                    locateBy: 'xpath',
                    locateValue: `//uni-view[contains(@class, 'fd-input-pwd')]//input`,
                    inputText: this.newPassword,
                },
                {
                    type: 'input',
                    locateBy: 'xpath',
                    locateValue: `//uni-view[contains(@class, 'fd-input-code')]//input`,
                    inputText: this.confirmPassword,
                },
                {
                    type: 'click',
                    locateBy: 'className',
                    locateValue: `uni-checkbox-wrapper`,
                    delay: 1,
                },
                {
                    type: 'click',
                    locateBy: 'className',
                    locateValue: `fd-login-btn`,
                    delay: 1,
                },
                {
                    type: 'click',
                    locateBy: 'xpath',
                    locateValue: `//uni-text[span[text()='返回登录']]`,
                },
            ]
            if (!this.isRegister) {
                elements = [
                    {
                        type: 'input',
                        locateBy: 'xpath',
                        locateValue: `//uni-view[contains(@class, 'fd-input-pwd')]//input`,
                        inputText: this.newPassword,
                    },
                    {
                        type: 'input',
                        locateBy: 'xpath',
                        locateValue: `//uni-view[contains(@class, 'fd-input-code')]//input`,
                        inputText: this.confirmPassword,
                        delay: 2,
                    },
                    {
                        type: 'click',
                        locateBy: 'className',
                        locateValue: `fd-login-btn`,
                    },
                ]
            }

            let res = await this.$rpa.operateElement(elements)
            
            if (res.all_success) {
                this.resetPasswordModal()
                this.passwordModalOpen = false
                await this.$rpa.refreshPage()
                this.$nextTick(() => {
                    setTimeout(() => {
                        this.$store.dispatch('loading/setLoading', false)
                    }, 300)
                    this.getQrOrCaptcha()
                })
                this.switchLoginType('扫码登录')
            }
        },

        // 验证密码规则
        validatePassword() {
            // 清除之前的错误信息
            this.passwordError = ''

            // 验证密码规则（8位以上的数字和字母）
            if (!this.newPassword) {
                this.passwordError = '请输入密码'
                return false
            }

            // 判断密码是否只包含数字和字母，且长度大于8位
            const passwordReg = /^[0-9a-zA-Z]{8,}$/
            if (!passwordReg.test(this.newPassword)) {
                this.passwordError = '密码只能包含数字和字母，且长度不少于8位'
                return false
            }

            return true
        },

        // 验证两次密码是否一致
        validatePasswordConfirm() {
            // 如果当前密码不符合规则，就不验证一致性
            if (!this.validatePassword()) {
                return false
            }

            // 验证两次密码是否一致
            if (this.newPassword !== this.confirmPassword) {
                this.passwordError = '两次输入的密码不一致'
                return false
            }

            // 清除之前的错误信息
            this.passwordError = ''
            return true
        },

        // 切换密码可见性
        togglePasswordVisibility() {
            this.passwordVisible = !this.passwordVisible
        },

        // 切换确认密码可见性
        toggleConfirmPasswordVisibility() {
            this.confirmPasswordVisible = !this.confirmPasswordVisible
        },
    },
    beforeUnmount() {
        if (this.timeout) {
            clearTimeout(this.timeout)
        }
        if (this.interval) {
            clearTimeout(this.interval)
        }
        if (this.registerCheckTimeout) {
            clearTimeout(this.registerCheckTimeout)
        }
    },
}
</script>
<style scoped>
.login_con {
    padding: 87px 128px;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
}

.left {
    font-family: Source Han Sans CN;
    font-weight: bold;
    font-size: 48px;
    color: #dbe6f1;
    text-align: center;
}

.right {
    width: 690px;
    height: 732px;
    background: rgba(219, 230, 241, 0.48);
    border-radius: 20px 20px 20px 20px;
    padding: 20px;
}

.tabs {
    width: 100%;
    display: flex;
}

.tab {
    width: 50%;
    height: 100px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #78808a;
    background: #dbe6f1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tab.active {
    color: #ffffff;
    background: #3173c6;
}

.content {
    width: 100%;
    height: 484px;
    background: #ffffff;
    border-radius: 0px 0px 20px 20px;
}

.scan_login {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 24px;
    color: #333333;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.scan_login img {
    width: 220px;
    height: 220px;
    background: #efefef;
    margin-bottom: 20px;
}

.account_login {
    padding: 40px;
}

.account_login img {
    margin: 0 20px;
}
/* 输入框 无边框 */
.account_login input {
    border: none;
    outline: none;
    background: #f1f5ff;
    height: 32px;
    font-size: 24px;
}

.account_login input::placeholder {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 24px;
    color: #a1b1c5;
}
.login_input {
    width: 100%;
    height: 72px;
    background: #f1f5ff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #a1b1c5;
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    position: relative;
}

.login_btn {
    height: 72px;
    background: #3173c6;
    border-radius: 10px 10px 10px 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 24px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 25px;
}

.captcha {
    height: 100%;
    margin: 0 !important;
    position: absolute;
    right: 0;
    cursor: pointer;
    border-radius: 0 10px 10px 0;
}
.other {
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 24px;
    color: #fff;
    margin-top: 30px;
}
.other div {
    height: 48px;
    line-height: 48px;
    display: flex;
}

.other .other_word {
    height: 58px;
    line-height: 72px;
}

.other .other_btn {
    height: 58px;
    line-height: 50px;
    font-size: 48px;
    border-bottom: 2px solid #fff;
}
</style>

<style>
.register-dialog .ant-modal-content {
    padding: 0;
    border-radius: 20px;
    overflow: hidden;
}

.register-dialog .ant-modal-title {
    height: 100px;
    background: #dbe6f1;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 42px;
    color: #03206a;
    display: flex;
    align-items: center;
    justify-content: center;
}

.register-dialog .modal_content {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 30px;
    color: #333333;
    padding: 0 50px 20px 50px;
    border-bottom: 2px solid #dbe6f1;
    display: flex;
    justify-content: center;
}

.register-dialog .modal_footer {
    height: 112px;
    padding: 0 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.register-dialog .modal_btn {
    width: 216px;
    height: 72px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #3173c6;
    border: 1px solid #3173c6;
    display: flex;
    align-items: center;
    justify-content: center;
}

.password-dialog .password-dialog-content {
    padding: 20px;
    background: rgba(109, 119, 136, 1);
}

.password-dialog-content-inner {
    background: #ffffff;
    border-radius: 20px;
}
.password-dialog .ant-modal-content {
    padding: 0;
    border-radius: 20px;
    overflow: hidden;
}

.password-dialog .modal_content {
    padding: 40px 40px 20px 40px;
    border-bottom: none;
    text-align: center;
}

.password-dialog .main-title {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 48px;
    color: #333333;
    margin-bottom: 25px;
    letter-spacing: 1px;
    text-align: left;
}

.password-dialog .subtitle {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-size: 32px;
    color: #78808a;
    margin-bottom: 8px;
    text-align: left;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.password-dialog .rules {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-size: 24px;
    color: #333333;
    margin-bottom: 25px;
    text-align: left;
    letter-spacing: 0.5px;
    font-weight: 500;
}

.password-dialog .input-wrapper {
    margin-bottom: 30px;
}

.password-dialog .input-container {
    position: relative;
    width: 100%;
}

.password-dialog .input-wrapper input {
    width: 100%;
    height: 72px;
    background: #f1f5ff;
    border: 1px solid #dcdfe6;
    border-radius: 8px;
    padding: 0 30px;
    font-size: 24px;
    box-sizing: border-box;
}

.password-dialog .input-wrapper input::placeholder {
    color: #a1b1c5;
    font-size: 24px;
}

.toggle-password {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    z-index: 1;
    font-size: 24px;
    color: #a1b1c5;
}

.toggle-password:hover {
    color: #3173c6;
}

.error-message {
    color: #ff4d4f;
    font-size: 20px;
    margin-top: 5px;
    text-align: left;
}

.password-dialog .agreement {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-size: 24px;
    color: #333333;
    margin-top: 10px;
    margin-bottom: 20px;
}

.password-dialog .agreement input[type='checkbox'] {
    width: 40px;
    height: 40px;
    margin-right: 8px;
}

.password-dialog .agreement a {
    color: #3173c6;
    text-decoration: none;
}

.password-dialog .modal_footer {
    height: auto;
    padding: 0 40px 40px 40px;
    display: flex;
    justify-content: center;
}

.password-dialog .modal_btn {
    width: 100%;
    height: 50px;
    background: #3173c6;
    border-radius: 8px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 20px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

/* 删除原有弹窗样式并保留全局样式 */
</style>