<template>
    <div class="date-input-group">
        <input ref="yearInput" v-model="year" @input="validateYear" @keydown="handleYearKeydown" placeholder="年份"
            maxlength="4" :class="{ 'error-value': yearError }" class="date-segment" />
        <span class="separator">-</span>
        <input ref="monthInput" v-model="month" @input="validateMonth" @keydown="handleMonthKeydown"
            @blur="handleMonthBlur" placeholder="月" maxlength="2" :class="{ 'error-value': monthError }"
            class="date-segment" :disabled="!validYear" />
        <span class="separator">-</span>
        <input ref="dayInput" v-model="day" @input="validateDay" @keydown="handleDayKeydown" @blur="handleDayBlur"
            placeholder="日" maxlength="2" :class="{ 'error-value': dayError }" class="date-segment"
            :disabled="!validMonth" />
    </div>
</template>
  
  <script>
export default {
    props: {
        modelValue: String, // 接收父组件的 v-model
    },
    data() {
        return {
            year: '',
            month: '',
            day: '',
            yearError: false,
            monthError: false,
            dayError: false,
            errorMessage: '',
        }
    },
    computed: {
        validYear() {
            return this.year.length === 4 && !this.yearError
        },
        validMonth() {
            return this.month.length === 2 && !this.monthError
        },
        validDay() {
            return this.day.length === 2 && !this.dayError
        },
    },
    watch: {
        // 监听分段值变化，实时拼接成完整日期
        year: 'emitDate',
        month: 'emitDate',
        day: 'emitDate',

        // 监听父组件传入的值（用于外部重置）
        modelValue: {
            immediate: true,
            handler(newVal) {
                if (newVal) {
                    const [y, m, d] = newVal.split('-')
                    this.year = y || ''
                    this.month = m || ''
                    this.day = d || ''
                } else {
                    this.year = ''
                    this.month = ''
                    this.day = ''
                }
            },
        },
    },
    methods: {
        // 向父组件发送完整日期
        emitDate() {
            let date = ''
            if (this.validYear) {
                date = this.year
                if (this.validMonth) {
                    date += `-${this.month}`
                    if (this.validDay) {
                        date += `-${this.day}`
                    }
                }
            }
            this.$emit('update:modelValue', date) // 关键！触发 v-model 更新
        },

        // 年份校验（1900-当前年份）
        validateYear() {
            this.year = this.year.replace(/\D/g, '')
            this.yearError = false

            if (this.year.length === 4) {
                const yearNum = parseInt(this.year)
                const currentYear = new Date().getFullYear()
                if (yearNum < 1900 || yearNum > currentYear) {
                    this.yearError = true
                    this.errorMessage = `年份需在1900-${currentYear}之间`
                } else {
                    this.$refs.monthInput.focus()
                    this.errorMessage = ''
                }
            }
        },

        // 月份校验（1-12）
        validateMonth() {
            this.month = this.month.replace(/\D/g, '')
            this.monthError = false
            if (this.month.length === 2) {
                const monthNum = parseInt(this.month)
                if (monthNum < 1 || monthNum > 12) {
                    this.monthError = true
                    this.errorMessage = '月份需在01-12之间'
                } else {
                    this.$refs.dayInput.focus()
                    this.errorMessage = ''
                }
            }
        },

        // 日期校验（根据年月）
        validateDay() {
            this.day = this.day.replace(/\D/g, '')
            this.dayError = false
            if (this.day.length === 2) {
                const dayNum = parseInt(this.day)
                const maxDay = this.getMaxDay()

                if (dayNum < 1 || dayNum > maxDay) {
                    this.dayError = true
                    this.errorMessage = `日期需在01-${maxDay}之间`
                } else {
                    this.errorMessage = ''
                }
            }
        },

        // 获取当月最大天数
        getMaxDay() {
            if (!this.validYear || !this.validMonth) return 31
            return new Date(
                parseInt(this.year),
                parseInt(this.month),
                0
            ).getDate()
        },

        // 键盘控制（退格键回退逻辑）
        handleYearKeydown(e) {
            if (e.key === 'Backspace' && this.year.length === 0) {
                e.preventDefault()
            }
        },
        handleMonthKeydown(e) {
            if (
                (e.key === 'Backspace' || e.key === 'Delete') &&
                this.month.length === 0
            ) {
                e.preventDefault()
                this.$refs.yearInput.focus()
                this.$nextTick(() => {
                    this.$refs.yearInput.setSelectionRange(3, 4)
                })
            }
        },
        handleMonthBlur() {
            if (this.month.length === 1 && this.month !== '0') {
                this.month = '0' + this.month
                this.emitDate()
            }
        },
        handleDayKeydown(e) {
            if (
                (e.key === 'Backspace' || e.key === 'Delete') &&
                this.day.length === 0
            ) {
                e.preventDefault()
                this.$refs.monthInput.focus()
                this.$nextTick(() => {
                    this.$refs.monthInput.setSelectionRange(1, 2)
                })
            }
        },
        handleDayBlur() {
            if (this.day.length === 1 && this.day !== '0') {
                this.day = '0' + this.day
                this.emitDate()
            }
        },
    },
}
</script>
  
  <style scoped>
input {
    border: none;
    outline: none;
    font-size: 24px;
}
input::placeholder {
    font-size: 24px;
}
input:first-child {
    width: 80px;
}
/* 保持之前的样式不变 */
.date-input-group {
    display: flex;
    align-items: center;
    height: 100%;
}
.date-segment {
    width: 40px;
    text-align: center;
}
.separator {
    margin: 0 5px;
    font-weight: bold;
}
.error-value {
    color: red;
}
.date-segment:disabled {
    cursor: not-allowed;
}
</style>