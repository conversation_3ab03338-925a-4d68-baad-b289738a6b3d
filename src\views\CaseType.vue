<template>
    <div class="action-con">
        <Container>
            <div class="con">
                请选择案件类型
                <div class="border">
                    <!-- <img src="@/assets/caseType/sqtj.png" @click="goAddType(caseType.SQTJ)" /> -->
                    <img src="@/assets/caseType/msys.png" @click="goAddType(caseType.MSYS)" />
                </div>
            </div>
        </Container>
    </div>
</template>

<script>
import Container from '@/components/Container.vue'
import { CASE_TYPE } from '@/utils/bizConstant'
export default {
    name: 'CaseType',
    components: {
        Container,
    },
    data() {
        return {
            caseType: CASE_TYPE,
        }
    },
    created() {
        if(this.$route.query.num == 1) {
            this.goAddType(this.caseType.MSYS)
        }
    },
    methods: {
        goAddType(ajlx) {
            this.$store.dispatch('layy/setCaseType', ajlx)    
            this.getAjlxBaseInfo(this.$store.getters.fyId, ajlx)
            this.$router.push('/addType')
        },

        // 获取案件类型基础信息
        async getAjlxBaseInfo(fyid, ajlx) {
            let res = await this.$rpa.dealFetch(
                `https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/pz/layymb/${fyid}/${ajlx}`,
                { method: 'GET' }
            )
            this.$store.dispatch('layy/setAjlxInfo', res.data)

        },
    },
}
</script>

<style scoped>
.con {
    display: flex;
    align-items: center;
    height: 100%;
    flex-direction: column;
    padding: 20px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 30px;
    color: #333333;
    font-weight: bold;
}
.con img {
    margin: 50px;
}

.border {
    width: 100%;
    height: 564px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #a1b1c5;
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>