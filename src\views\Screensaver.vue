<template>
    <div class="screensaver" @click="goToLogin">
        <div class="screensaver-content">
            <img src="@/assets/court.png" alt="court" class="screensaver-logo" />
            <div class="courtName">{{courtName}}</div>
            <div class="welcome-text">欢迎使用{{deviceName}}</div>
        </div>
    </div>
</template>
  
  <script>
import { resetStore } from '@/store'
import { checkUpdate } from '@/utils/update'
export default {
    name: 'Screensaver',
    inject: ['courtName', 'deviceName'],
    methods: {
        async goToLogin() {
            await this.$rpa.get(
                'https://zxfw.court.gov.cn/zxfw/index.html#/pagesGrxx/pc/login/index'
            )
            this.$router.push('/login')
        },
    },
    mounted() {
        this.$rpa.resetBrowser()
        localStorage.clear()
        resetStore()
        try {
            checkUpdate()
        } catch (e) {}
    },
}
</script>
  
  <style scoped>
.screensaver {
    width: 100%;
    height: 100%;
    background: url('../assets/screenSaver.png') no-repeat center center;
    color: white;
    display: flex;
    justify-content: center;
    cursor: pointer;
}

.screensaver-logo {
    margin-top: 232px;
}

.courtName {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 52px;
    line-height: 52px;
    color: #fee600;
    letter-spacing: 4px;
}

.welcome-text {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 72px;
    color: #f1f5ff;
    letter-spacing: 8px;
    margin-top: 72px;
    line-height: 72px;
}
.screensaver-content {
    text-align: center;
}
</style>