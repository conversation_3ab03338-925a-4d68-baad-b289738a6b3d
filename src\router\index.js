import { createRouter, createWebHashHistory } from 'vue-router'
import Screensaver from '@/views/Screensaver.vue'
import Login from '@/views/Login.vue'
import ActionType from '@/views/ActionType.vue'
import Home from '@/views/Home.vue'
import CaseType from '@/views/CaseType.vue'
import AddType from '@/views/AddType.vue'
import UserType from '@/views/UserType.vue'
import IpType from '@/views/IpType.vue'
import Step1 from '@/views/filingCase/Step1.vue'
import Step2 from '@/views/filingCase/Step2.vue'
import Step30 from '@/views/filingCase/Step3-0.vue'
import Step3 from '@/views/filingCase/Step3-1.vue'
import Step40 from '@/views/filingCase/Step4-0.vue'
import Step41 from '@/views/filingCase/Step4-1.vue'
import Step5 from '@/views/filingCase/Step5.vue'
import Step6 from '@/views/filingCase/Step6.vue'
import MyCaseList from '@/views/MyCaseList.vue'

const routes = [
  {
    path: '/',
    name: 'Screensaver',
    component: Screensaver,
    meta: { showHeader: false } // 屏保页不显示header
  },
  {
    path: '/login',
    name: 'Login',
    component: Login
    // 不设置meta.showHeader，默认显示header
  },
  {
    path: '/actionType',
    name: 'ActionType',
    component: ActionType
    // 不设置meta.showHeader，默认显示header
  },
  {
    path: '/caseType',
    name: 'CaseType',
    component: CaseType
    // 不设置meta.showHeader，默认显示header
  },
  {
    path: '/addType',
    name: 'AddType',
    component: AddType
    // 不设置meta.showHeader，默认显示header
  },
  {
    path: '/userType',
    name: 'UserType',
    component: UserType
    // 不设置meta.showHeader，默认显示header
  },
  {
    path: '/ipType',
    name: 'IpType',
    component: IpType
    // 不设置meta.showHeader，默认显示header
  },
  {
    path: '/step1',
    name: 'Step1',
    component: Step1
    // 不设置meta.showHeader，默认显示header
  },
  {
    path: '/step2',
    name: 'Step2',
    component: Step2
    // 不设置meta.showHeader，默认显示header
  },
  {
    path: '/step3-0',
    name: 'Step3-0',
    component: Step30
    // 不设置meta.showHeader，默认显示header
  },
  {
    path: '/step3',
    name: 'Step3',
    component: Step3
    // 不设置meta.showHeader，默认显示header
  },
  {
    path: '/step4',
    name: 'Step4',
    component: Step40
    // 不设置meta.showHeader，默认显示header
  },
  {
    path: '/step4-1',
    name: 'Step4-1',
    component: Step41
    // 不设置meta.showHeader，默认显示header
  },
  {
    path: '/step5',
    name: 'Step5',
    component: Step5
    // 不设置meta.showHeader，默认显示header
  },
  {
    path: '/step6',
    name: 'Step6',
    component: Step6
    // 不设置meta.showHeader，默认显示header
  },
  {
    path: '/home',
    name: 'Home',
    component: Home
    // 不设置meta.showHeader，默认显示header
  },
  {
    path: '/myCaseList',
    name: 'MyCaseList',
    component: MyCaseList
    // 不设置meta.showHeader，默认显示header
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router