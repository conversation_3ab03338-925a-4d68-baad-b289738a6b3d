// imgRecNode.js - 在Express服务器中使用imgRec的API示例

// 导入所需模块
const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const imgRec = require('../utils/imgRec');

// 创建Express应用
const app = express();
const PORT = process.env.PORT || 3000;

// 使用中间件
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// 定义API路由
app.post('/api/classify', (req, res) => {
  try {
    // 获取请求中的文本内容
    const { text } = req.body;
    
    // 验证输入
    if (!text || typeof text !== 'string') {
      return res.status(400).json({
        success: false,
        message: '请提供有效的文本内容'
      });
    }
    
    // 使用imgRec进行分类
    const result = imgRec(text);
    
    // 返回分类结果
    return res.status(200).json({
      success: true,
      data: {
        category: result.category,
        confidence: result.confidence,
        allConfidences: result.allConfidences,
        text: result.text
      }
    });
  } catch (error) {
    console.error('分类错误:', error);
    return res.status(500).json({
      success: false,
      message: `处理出错: ${error.message}`
    });
  }
});

/**
 * 启动服务器
 * 使用方法：
 * 1. 安装依赖: npm install express body-parser cors
 * 2. 运行服务器: node imgRecNode.js
 * 3. 通过API调用:
 *    POST http://localhost:3000/api/classify
 *    请求体: { "text": "需要分类的文本内容" }
 */
function startServer() {
  app.listen(PORT, () => {
    console.log(`API服务器已启动，监听端口 ${PORT}`);
    console.log(`文本分类API: http://localhost:${PORT}/api/classify`);
  });
}

// 如果直接运行此文件，则启动服务器
if (require.main === module) {
  startServer();
}

// 导出服务器应用，供其他模块使用
module.exports = app; 