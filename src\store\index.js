import { createStore } from 'vuex'
import getters from './getters'
import user from './modules/user'
import layy from './modules/layy'
import loading from './modules/loading'
import createPersistedState from 'vuex-persistedstate';

function getInitialState() {
    return {
        user: user.state(),
        layy: layy.state(),
        loading: loading.state(),
    }
}

// 使用Vue 3创建store
const store = createStore({
  modules: {
    user,
    layy,
    loading
  },
  getters,
  plugins: [
    createPersistedState({
      key: 'dq', // 存储的 key（默认 'dq'）
      storage: window.localStorage, // 使用 localStorage
    }),
  ],
})

export function resetStore() {
  store.replaceState(getInitialState());
}

export default store
