// 根据文字对图片进行分类
function imgRec(text) {

    // 各类文档的关键词特征
    const KEYWORDS = {
        "起诉状": ["起诉状", "原告", "被告", "诉讼请求", "事实与理由", "此致", "法院", "具状人"],
        "自然人身份证明": ["中华人民共和国", "居民身份证"],
        "法人身份证明": ["法定代表人", "身份证明书", "单位名称", "单位性质", "地址", "成立时间", "经营期限", "姓名", "性别", "年龄","职务" ],
        "营业执照身份证明": ["营业执照","统一社会信用代码", "证照编号", "名称", "类型", "法定代表人", "经营资本", "成立日期", "住所", "登记机关"],
        "组织机构代码证身份证明": ["组织机构代码证", "代码", "机构名称", "机构类型", "地址", "有效期", "颁发单位", "登记号", "说明", "国家质量监督检验检疫总局签章","年检记录" ],
        "委托代理人委托手续和身份材料": ["委托书", "委托", "代为", "代理人", "受托人", "律师事务所", "律师", "授权", "代理权限", "特别授权"],
        "送达地址确认书": ["送达地址确认书", "送达方式", "送达地址", "传真", "手机号码", "电话", "联系方式", "邮政编码", "送达人"],
        "证据目录及证据材料": ["证据", "清单", "目录", "提交人", "证据一", "证据二", "证据三", "证明事项"]
    };
    
    // 根据文本进行文档分类
    function classifyDocument(text) {  
        // 对每个类别计算匹配得分
        const scores = {};
        for (const category in KEYWORDS) {
            const keywords = KEYWORDS[category];
            scores[category] = calculateKeywordScore(text, keywords);
        }
        
        // 找出得分最高的类别
        let maxCategory = Object.keys(scores).reduce((a, b) => scores[a] > scores[b] ? a : b);
        
        // 如果最高分低于阈值，归为证据目录及证据材料
        if (scores[maxCategory] < 0.3) { // 阈值可调整
            return "证据目录及证据材料";
        }
            
        return maxCategory;
    }
    
    // 计算文本中包含关键词的比例
    function calculateKeywordScore(text, keywords) {
        const matches = keywords.filter(keyword => text.includes(keyword)).length;
        return keywords.length ? matches / keywords.length : 0;
    }
    
    // 计算分类结果的置信度
    function calculateConfidence(text, category) {
        // 如果类别是"证据目录及证据材料"且没有对应的关键词列表
        if (category === "证据目录及证据材料" && !KEYWORDS[category]) {
            return 0.5; // 默认置信度
        }
        
        const keywords = KEYWORDS[category] || [];
        const matches = keywords.filter(keyword => text.includes(keyword)).length;
        return Math.min(1.0, matches / Math.max(5, keywords.length * 0.5));
    }
    
    // 计算所有类别的置信度
    function calculateAllConfidences(text) {
        const confidences = {};
        
        // 为每个类别计算置信度
        for (const category in KEYWORDS) {
            const keywords = KEYWORDS[category] || [];
            const matches = keywords.filter(keyword => text.includes(keyword)).length;
            confidences[category] = Math.min(1.0, matches / Math.max(5, keywords.length * 0.5));
        }
        
        // 添加证据目录类别（如果没有在KEYWORDS中定义）
        if (!KEYWORDS["证据目录及证据材料"]) {
            confidences["证据目录及证据材料"] = 0.5; // 默认置信度
        }
        
        return confidences;
    }
    
    // 进行分类并返回结果
    const category = classifyDocument(text);
    const confidence = calculateConfidence(text, category);
    const allConfidences = calculateAllConfidences(text); // 计算所有类别的置信度
    
    return {
        category: category,
        confidence: confidence,
        allConfidences: allConfidences, // 添加所有类别的置信度
        text: text
    };
}

// 使用ESM导出方式
export default imgRec;
