<template>
    <div class="receipt-container">
        <div class="receipt-header">
            <h2>法院小票</h2>
            <div class="receipt-number">{{ layyid}}</div>
        </div>

        <div class="receipt-body">
            <!-- 基本信息 -->
            <div class="receipt-section">
                <div class="receipt-row">
                    <span class="label">案件类型:</span>
                    <span class="value">{{ layy.cajlx }}</span>
                </div>
                <div class="receipt-row">
                    <span class="label">操作人:</span>
                    <span class="value">{{ username }}</span>
                </div>
                <div class="receipt-row">
                    <span class="label">操作时间:</span>
                    <span class="value">{{ operationTime }}</span>
                </div>
                <div class="receipt-row">
                    <span class="label">操作地点:</span>
                    <span class="value">{{ layy.fymc }}</span>
                </div>
            </div>

            <!-- 原告信息 -->
            <div class="receipt-section">
                <div class="receipt-row">
                    <span class="label">当事人:</span>
                    <span class="value">
                        {{ displayPlaintiffs }}
                        <span v-if="dsrs.length > 3" class="more-text">等{{ dsrs.length }}人</span>
                    </span>
                </div>
            </div>

            <!-- 备注信息 -->
            <div class="receipt-section">
                <div class="receipt-row">
                    <span class="label">案由:</span>
                    <span class="value">{{ layy.laay }}</span>
                </div>
            </div>
        </div>

        <img src="@/assets/qrcode.jpg"
            style="width: 250px; height: 250px; margin-top: 10px; display: block; margin-left: auto; margin-right: auto;" />

        <div class="receipt-footer">
            <div class="print-time">打印时间: {{ printTime }}</div>
        </div>
    </div>
</template>
  
  <script>
export default {
    name: 'Receipt',
    props: {
        operationTime: {
            type: String,
            default: () => new Date().toLocaleString(),
        },
    },
    data() {
        return {
            dsrs: this.$store.getters.layyInfo.dsrs,
            layyid: this.$store.getters.layyid,
            layy: this.$store.getters.layyInfo.layy,
            username: this.$store.getters.currentUserInfo.username,
        }
    },
    computed: {
        displayPlaintiffs() {
            return this.dsrs
                .map((t) => t.xm || t.dwmc)
                .slice(0, 3)
                .join('、')
        },
        printTime() {
            return new Date().toLocaleString()
        },
    },
    mounted() {
        this.getLayyDetail()
    },
    methods: {
        async getLayyDetail() {
            const layyId =
                this.$store.getters.layyId || '16aaf4ddb3904d0383ff90396fd0c051'
            let layyInfoRes = await this.$rpa.dealFetch(
                `https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/layyxq/${layyId}/0`,
                { method: 'GET' }
            )
            if (layyInfoRes.code === 200 && layyInfoRes.data) {
                this.$store.dispatch('layy/setLayyInfo', layyInfoRes.data)
            }
        },
    },
}
</script>
  
  <style scoped>
.receipt-container {
    width: 384px;
    margin: 20px auto;
    padding: 20px;
    border: 1px solid #ddd;
    font-family: 'SimSun', '宋体', serif;
    background-color: #fff;
}

.receipt-header {
    text-align: center;
    margin-bottom: 20px;
    border-bottom: 2px solid #000;
    padding-bottom: 10px;
}

.receipt-header h2 {
    margin: 0;
    font-size: 26px;
    font-weight: bold;
}

.receipt-number {
    font-size: 16px;
    color: #666;
    margin-top: 5px;
}

.receipt-body {
    margin: 20px 0;
    font-size: 22px;
}

.receipt-section {
    margin-bottom: 15px;
}

.receipt-section h3 {
    margin: 10px 0;
    font-size: 12px;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}

.receipt-row {
    display: flex;
    margin-bottom: 8px;
    line-height: 1.5;
}

.label {
    font-weight: bold;
    min-width: 100px;
    margin-right: 10px;
}

.value {
    flex: 1;
}

.more-text {
    color: #666;
    font-style: italic;
}

.receipt-footer {
    margin-top: 30px;
    text-align: right;
    border-top: 1px dashed #000;
    padding-top: 10px;
}

.print-time {
    font-size: 16px;
    color: #666;
}

@media print {
    .receipt-container {
        box-shadow: none;
        border: none;
        padding: 0;
    }
}
</style>