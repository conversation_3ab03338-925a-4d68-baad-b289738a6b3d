<template>
    <div>
        <a-modal :open="open" title="邮箱送达地址" :footer="null" :closable="false" width="1087px" :centered="true"
            wrapClassName="email-address-dialog" :maskClosable="false">
            <div class="modal_content">
                <div class="email-form">
                    <FormItem label="邮箱送达地址" :required="true" :span="2">
                        <input v-model="form.email" placeholder="请输入邮箱送达地址" />
                    </FormItem>
                </div>
            </div>
            <div class="modal_footer">
                <div class="modal_btn normal" @click="$emit('update:open', false)">
                    取消
                </div>
                <div class="modal_btn" @click="submit">
                    提交
                </div>
            </div>
        </a-modal>
    </div>
</template>

<script>
import FormItem from '@/components/FormItem.vue'
export default {
    components: {
        FormItem,
    },
    props: {
        open: {
            type: Boolean,
            required: true,
        },
    },
    data() {
        return {
            form: {
                bh: '',
                email: '',
            },
        }
    },
    methods: {
        submit() {
            // 验证邮箱地址格式
            const emailRegex =
                /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
            if (!emailRegex.test(this.form.email)) {
                this.$message.error('请输入正确的邮箱地址')
                return
            }
            this.$rpa
                .dealFetch(
                    'https://zxfw.court.gov.cn/yzw/yzw-zxfw-yhfw/api/v1/grxx/yxsd/add',
                    {
                        method: 'POST',
                        body: this.form,
                    }
                )
                .then((res) => {
                    if (res.code == 200) {
                        this.$message.success('提交成功')
                        this.$emit('updateList')
                        this.$emit('update:open', false)
                    }
                })
            this.$emit('update:open', false)
        },
    },
}
</script>


<style scoped>
.email-form {
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
}
</style>

<style>
.email-address-dialog .ant-modal-content {
    padding: 0;
    border-radius: 20px;
    overflow: hidden;
}

.email-address-dialog .ant-modal-title {
    height: 100px;
    background: #dbe6f1;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 42px;
    color: #03206a;
    display: flex;
    align-items: center;
    justify-content: center;
}

.email-address-dialog .modal_content {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 24px;
    color: #333333;
    padding: 20px 40px;
    border-bottom: 2px solid #dbe6f1;
    height: 670px;
}

.email-address-dialog .modal_footer {
    height: 112px;
    padding: 0 50px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.email-address-dialog .modal_btn {
    width: 216px;
    height: 72px;
    background: #3173c6;
    border-radius: 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20px;
    cursor: pointer;
    border: none;
}
.email-address-dialog .modal_btn.normal {
    background: #fff;
    color: #3173c6;
    border: 1px solid #3173c6;
}
</style>

