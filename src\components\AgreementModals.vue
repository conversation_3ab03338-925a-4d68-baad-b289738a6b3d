<template>
    <!-- 注册协议弹窗 -->
    <a-modal :open="agreementModalOpen" @update:open="$emit('update:agreementModalOpen', $event)" title="人民法院在线服务平台注册协议" :footer="null" :closable="false" width="1478px"
        :centered="true" wrapClassName="register-agreement agreement-modal" :maskClosable="false" @cancel="closeAgreementModal">
        <div class="agreement-content-body">
            <div class="agreement-content">
                <div class="agreement-section">
                    <h3 class="agreement-heading">1.服务条款的确认和接纳</h3>
                    <p class="agreement-paragraph">
                        人民法院在线服务平台用户服务的所有权和运作权归人民法院在线服务平台所有。人民法院在线服务平台所提供的服务将按照有关章程、服务条款和操作规则严格执行。用户必须完全同意所有服务条款并完成账户激活程序，才能成为人民法院在线服务平台的正式用户。
                    </p>
                </div>
                <div class="agreement-section">
                    <h3 class="agreement-heading">2.用户应做到</h3>
                    <p class="agreement-paragraph">1）依照本网站的要求提供真实、详尽、准确的个人材料。</p>
                    <p class="agreement-paragraph">2）以上相关材料发生变更时依照真实、详尽、准确的要求及时进行更新。</p>
                    <p class="agreement-paragraph">
                        3）当事人或诉讼代理人应诚信诉讼。同意进入本平台后，在本平台上发表的所有文字、语音、视频、图片等均视为本人操作，由本人承担相应的法律责任。</p>
                    <p class="agreement-paragraph">
                        4）当事人及其诉讼代理人同意进入本平台参与诉讼活动的，在案件审理期间不得擅自退出本平台。进入平台的微信号即视为当事人或诉讼代理人已确认的电子送达地址，人民法院将依法开展电子送达。
                    </p>
                    <p class="agreement-paragraph">5）通过本平台进行的诉讼行为效力等同于线下诉讼行为的效力，线上电子签名与线下签名有同等效力。</p>
                    <p class="agreement-paragraph">6）当事人及其诉讼代理人可通过平台联系法官、提供证据、参与调解、申请保全等诉讼活动。</p>
                    <p class="agreement-paragraph">
                        7）当事人及其诉讼代理人进入平台后的言行应合法，不得发表与案件无关的言论、视频、图片等。对在本平台所形成的文字、语音、视频、图片等内容不得用于与诉讼无关的任何其他用途，不得外传、扩散、截屏、转发他人。
                    </p>
                    <p class="agreement-paragraph">
                        8）当事人及其诉讼代理人应保持手机畅通，若在诉讼过程中发生手机遗失、微信被盗等特殊情形时，应及时告知承办人，并采取补救措施，在此期间所产生的一切法律后果均由当事人本人承担。
                    </p>
                </div>
                <div class="agreement-section">
                    <h3 class="agreement-heading">3.人民法院在线服务平台会不定期地修改服务条款，服务条款一旦
                        发生变动，将会在相关页面上提示修改内容。如果您同意改动，则再一次点击"我同意"按钮。如果您不接受，则及时取消您的用户使用服务资格。</h3>
                </div>
                <div class="agreement-section">
                    <h3 class="agreement-heading">4.人民法院在线服务平台保留随时修改或中断服务而不需通知用户
                        的权利。人民法院在线服务平台行使修改或中断服务的权利，不需对 用户或任何第三方负责。</h3>
                </div>
                <div class="agreement-section">
                    <h3 class="agreement-heading">5.尊重用户个人隐私是人民法院在线服务平台的基本原则。人民法
                        院在线服务平台不会公开、编辑或透露用户的账户信息，除非有法律 许可要求，或人民法院在线服务平台在诚信的基础上认为透露这些信
                        息在以下三种情况是必要的；</h3>
                    <p class="agreement-paragraph">1）遵守有关法律规定，遵从合法服务程序。</p>
                    <p class="agreement-paragraph">2）在紧急情况下用以维护用户个人和社会大众的隐私安全。</p>
                    <p class="agreement-paragraph">3）符合其他国家法律法规的相关要求。</p>
                </div>
                <div class="agreement-section">
                    <h3 class="agreement-heading">
                        6.一旦成功成为人民法院在线服务平台的用户，您将得到一个密码
                        和账号。请谨慎保管您的账号及密码，因疏于保管账号密码造成的后果由用户自己承担。另外，每个用户都要对其账户中的所有活动和事件负全责。您可根据实际情况随时改变您的密码。用户若发现任何非法使用用户账户和安全漏洞的情况，请立即通知人民法院在线服务平台。
                    </h3>
                </div>
                <div class="agreement-section">
                    <h3 class="agreement-heading">
                        7.用户明确同意其使用人民法院在线服务平台所存在的风险将完全
                        由其自己承担；因其使用人民法院在线服务平台而产生的一切后果也 由其自己承担，人民法院在线服务平台对用户不承担任何责任。
                    </h3>
                </div>
                <div class="agreement-section">
                    <h3 class="agreement-heading">8.人民法院在线服务平台对任何直接、间接、偶然、特殊及继起的损害不负责任。</h3>
                </div>
                <div class="agreement-section">
                    <h3 class="agreement-heading">9.用户单独承担传输内容的责任。用户必须遵守：</h3>
                    <p class="agreement-paragraph">1）从中国境内向外传输相关资料时必须符合中国有关法律、法规。</p>
                    <p class="agreement-paragraph">2）使用网站服务不做非法用途。</p>
                    <p class="agreement-paragraph">3）不扰乱本网站的各项服务。</p>
                    <p class="agreement-paragraph">4）不在网站上发表任何可能产生恶劣影响的信息。</p>
                    <p class="agreement-paragraph">5）严格遵守使用网站服务的网络协议、规定、程序和惯例。</p>
                    <p class="agreement-paragraph">6）不得利用本站危害国家安全、泄露国家秘密，不得侵犯国家、社会、集体和公民的合法权益。</p>
                    <p class="agreement-paragraph">7）不得利用本站制作、复制和传播不法信息。</p>
                </div>
                <div class="agreement-section">
                    <h3 class="agreement-heading">10.网站内容的所有权</h3>
                    <p class="agreement-paragraph">
                        人民法院在线服务平台定义的内容包括：文字、软件、声音、图片、视频、图表；以上内容的所有权归人民法院在线服务平台所有，用户只能在人民法院在线服务平台的授权下才能使用这些内容，而不能擅自复制、篡改，不能创造与内容有关的派生产品。
                    </p>
                </div>
                <div class="agreement-section">
                    <h3 class="agreement-heading">11.解释权</h3>
                    <p class="agreement-paragraph">
                        本注册协议的解释权归人民法院在线服务平台所有，如其中任何条款与国家法律法规相抵触，则以国家法律法规的规定为准。</p>
                </div>
            </div>
            <div class="pageBar">
                <img src="@/assets/pageUp.png" @touchstart="startScrolling(-1)" @touchend="stopScrolling" @mousedown="startScrolling(-1)" @mouseup="stopScrolling" @mouseleave="stopScrolling" />
                <img src="@/assets/pageDown.png" @touchstart="startScrolling(1)" @touchend="stopScrolling" @mousedown="startScrolling(1)" @mouseup="stopScrolling" @mouseleave="stopScrolling" />
            </div>
        </div>
        <div class="modal_footer register-agreement-footer">
            <div class="modal_btn" @click="closeAgreementModal">
                关闭
            </div>
        </div>
    </a-modal>

    <!-- 隐私政策弹窗 -->
    <a-modal :open="privacyPolicyModalOpen" @update:open="$emit('update:privacyPolicyModalOpen', $event)" title="人民法院在线服务小程序隐私保护" :footer="null" :closable="false"
        width="1478px" :centered="true" wrapClassName="register-agreement privacy-modal" :maskClosable="false"
        @cancel="closePrivacyPolicyModal">
        <div class="agreement-content-body">
            <div class="agreement-content">
                <p class="agreement-paragraph" style="text-indent: 2em; padding-left: 0;">
                    本指引是人民法院在线服务小程序开发者"中华人民共和国最高人民法院"（以下简称"开发者"）为处理你的个人信息而制定。
                </p>
                <div class="agreement-section">
                    <h3 class="agreement-heading">1.开发者处理的信息</h3>
                    <p class="agreement-paragraph">· 为了实名认证，开发者将在获取你的明示同意后，收集你的微信昵称、头像。</p>
                    <p class="agreement-paragraph">· 为了在线立案、在线庭审等功能，开发者将在获取你的明示同意后，收集你的位置信息。</p>
                    <p class="agreement-paragraph">· 为了在线立案、在线庭审、代理见证等功能，开发者将在获取你的明示同意后，访问你的麦克风。</p>
                    <p class="agreement-paragraph">
                        · 为了扫一扫、图片上传、人脸识别认证、在线庭审、实名认证人工审核、在线立案、图片转文字、电子签名、代理见证等功能，开发者将在获取你的明示同意后，访问你的摄像头。
                    </p>
                    <p class="agreement-paragraph">· 为了人民法院在线服务小程序登录，开发者将在获取你的明示同意后，收集你的手机号。</p>
                    <p class="agreement-paragraph">
                        · 为了扫一扫、图片上传、在线立案、送达列表、图片转文字、电子签名、实名认证人工审核、文件列表等功能，开发者将在获取你的明示同意后，使用你的相册（仅写入）权限。
                    </p>
                    <p class="agreement-paragraph">
                        · 开发者收集你选中的照片或视频信息，用于在立案、诉讼功能使用中可让用户通过相册进行添加提交材料，提交证据以及上传对应文件等功能。
                    </p>
                    <p class="agreement-paragraph">·
                        开发者收集你选中的文件，用于立案、诉讼功能中使用这可选择对应的文件如文档文件，图片文件和录音视频文件等功能。</p>
                    <p class="agreement-paragraph">· 开发者获取你选择的位置信息，用于送达地址确认书等功能。</p>
                    <p class="agreement-paragraph">· 开发者收集你的地址，用于送达地址确认书等功能。</p>
                    <p class="agreement-paragraph">·
                        开发者收集你的操作日志，用于接入开发者服务的其他程序所提供的配置信息、您的IP地址和移动设备所用的版本和设备识别码等。</p>
                    <p class="agreement-paragraph">· 开发者收集你的身份证号码，用于登录、诉讼服务等功能。</p>
                    <p class="agreement-paragraph">· 开发者读取你的剪切板，用于视频回放，语音转写等功能的复制粘贴信息</p>
                </div>
                <div class="agreement-section">
                    <h3 class="agreement-heading">2.你的权益</h3>
                    <p class="agreement-paragraph">
                        2.1关于收集你的位置信息、访问你的麦克风、访问你的摄像头、使用你的相册（仅写入）权限、获取你选择的位置信息，你可以通过以下路径：小程序主页右上角"…"—"设置"—点击特定信息—点击"不允许"，撤回对开发者的授权。
                    </p>
                    <p class="agreement-paragraph">
                        2.2关于收集你的手机号、收集你选中的照片或视频信息、收集你选中的文件、读取你的剪切板，你可以通过以下路径：小程序主页右上角"..."
                        — "设置" — "小程序已获取的信息" —
                        点击特定信息—点击"通知开发者删除"，开发者承诺收到通知后将删除信息。法律法规另有规定的，开发者承诺将停止除存储和采取必要的安全保护措施之外的处理。
                    </p>
                    <p class="agreement-paragraph">2.3关于你的个人信息，你可以通过与开发者联系，行使查阅、复制、更正、删除等法定权利。</p>
                    <p class="agreement-paragraph">2.4若你在小程序中注册了账号，你可以注销你在小程序中使用的账号。</p>
                </div>
                <div class="agreement-section">
                    <h3 class="agreement-heading">3.开发者对信息的存储</h3>
                    <p class="agreement-paragraph">
                        3.1开发者承诺，除法律法规另有规定外，开发者对你的信息的保存期限应当为实现处理目的所必要的最短时间。</p>
                </div>
                <div class="agreement-section">
                    <h3 class="agreement-heading">4.信息的使用规则</h3>
                    <p class="agreement-paragraph">4.1开发者将会在本指引所明示的用途内使用收集的信息。</p>
                    <p class="agreement-paragraph">
                        4.2如开发者使用你的信息超出本指引目的或合理范围，开发者必须在变更使用目的或范围前，再次以站内信方式告知并征得你的明示同意。
                    </p>
                </div>
                <div class="agreement-section">
                    <h3 class="agreement-heading">5.信息对外提供</h3>
                    <p class="agreement-paragraph">
                        5.1开发者承诺，不会主动共享或转让你的信息至任何第三方，如存在确需共享或转让时，开发者应当直接征得或确认第三方征得你的单独同意。
                    </p>
                    <p class="agreement-paragraph">
                        5.2开发者承诺，不会对外公开披露你的信息，如必须公开披露时，开发者应当向你告知公开披露的目的、披露信息的类型及可能涉及的信息，并征得你的单独同意。
                    </p>
                </div>
                <div class="agreement-section">
                    <h3 class="agreement-heading">6.若你认为开发者未遵守上述约定，或有其他的投诉建议、或未成年人个人信息保护相关问题，可与开发者联系；或者向微信进行投诉。
                    </h3>
                </div>
                <p class="agreement-paragraph" style="text-indent: 0; padding-left: 0;">更新日期：2024年XX月XX日</p>
                <p class="agreement-paragraph" style="text-indent: 0; padding-left: 0;">生效日期：2024年XX月XX日</p>
            </div>
            <div class="pageBar">
                <img src="@/assets/pageUp.png" @touchstart="startScrolling(-1)" @touchend="stopScrolling" @mousedown="startScrolling(-1)" @mouseup="stopScrolling" @mouseleave="stopScrolling" />
                <img src="@/assets/pageDown.png" @touchstart="startScrolling(1)" @touchend="stopScrolling" @mousedown="startScrolling(1)" @mouseup="stopScrolling" @mouseleave="stopScrolling" />
            </div>
        </div>
        <div class="modal_footer register-agreement-footer">
            <div class="modal_btn" @click="closePrivacyPolicyModal">
                关闭
            </div>
        </div>
    </a-modal>
</template>

<script>
export default {
    name: 'AgreementModals',
    props: {
        agreementModalOpen: {
            type: Boolean,
            default: false
        },
        privacyPolicyModalOpen: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            scrollStep: 10, // 每次滚动的像素
            scrollInterval: null, // 用于控制连续滚动的定时器
            scrollSpeed: 30 // 滚动速度（毫秒），数值越小滚动越快
        }
    },
    emits: ['update:agreementModalOpen', 'update:privacyPolicyModalOpen'],
    methods: {
        closeAgreementModal() {
            this.$emit('update:agreementModalOpen', false);
        },
        closePrivacyPolicyModal() {
            this.$emit('update:privacyPolicyModalOpen', false);
        },
        // 开始连续滚动
        startScrolling(direction) {
            // 先执行一次滚动
            this.scrollPage(direction);
            
            // 清除可能存在的之前的定时器
            if (this.scrollInterval) {
                clearInterval(this.scrollInterval);
            }
            
            // 设置连续滚动的定时器
            this.scrollInterval = setInterval(() => {
                this.scrollPage(direction);
            }, this.scrollSpeed);
        },
        // 停止滚动
        stopScrolling() {
            if (this.scrollInterval) {
                clearInterval(this.scrollInterval);
                this.scrollInterval = null;
            }
        },
        // 执行滚动操作
        scrollPage(direction) {
            // 获取当前显示的内容区域
            let content = null;
            if (this.agreementModalOpen) {
                content = document.querySelector('.agreement-modal .agreement-content');
            } else if (this.privacyPolicyModalOpen) {
                content = document.querySelector('.privacy-modal .agreement-content');
            }
            
            if (content) {
                // 向上滚动为负，向下滚动为正
                content.scrollTop += direction * this.scrollStep;
            }
        }
    },
    // 确保组件销毁时清除定时器
    beforeUnmount() {
        this.stopScrolling();
    }
}
</script>

<style>
/* 样式保持不变，可以从 Login.vue 中复制 */
.register-agreement .ant-modal-content {
    padding: 0;
    border-radius: 20px;
    overflow: hidden;
}

.register-agreement .ant-modal-title {
    height: 100px;
    background: #dbe6f1;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 32px;
    color: #03206a;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

.register-agreement .agreement-content-body {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-size: 16px;
    color: #333333;
    padding: 20px 50px;
    padding-top: 0px;
    position: relative;
    max-height: 60vh;
    overflow: hidden;
}

/* 内容区域，隐藏滚动条但可滚动 */
.register-agreement .agreement-content {
    max-height: 60vh;
    overflow-y: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

/* WebKit浏览器隐藏滚动条 */
.register-agreement .agreement-content::-webkit-scrollbar {
    display: none;
}

/* 翻页按钮容器 */
.register-agreement .pageBar {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 50px; /* 增加间距 */
}

.register-agreement .pageBar img {
    width: 40px;  /* 略微增大按钮尺寸 */
    height: 40px;
    cursor: pointer;
    /* 防止长按时出现系统菜单或选中 */
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
}

.register-agreement .agreement-section {
    margin-bottom: 20px;
}

.register-agreement .agreement-heading {
    font-weight: bold;
    font-size: 18px;
    margin-bottom: 10px;
}

.register-agreement .agreement-paragraph {
    line-height: 1.8;
    margin-bottom: 5px;
    text-indent: 2em;
}

/* Special case for list-like paragraphs */
.register-agreement .agreement-section .agreement-paragraph {
    text-indent: 0;
    padding-left: 2em;
}

.register-agreement .agreement-section:first-of-type .agreement-paragraph,
.register-agreement .agreement-section:nth-of-type(6) .agreement-paragraph,
.register-agreement .agreement-section:nth-of-type(7) .agreement-paragraph,
.register-agreement .agreement-section:nth-of-type(8) .agreement-paragraph,
.register-agreement .agreement-section:nth-of-type(10) .agreement-paragraph,
.register-agreement .agreement-section:nth-of-type(11) .agreement-paragraph {
    text-indent: 2em;
    padding-left: 0;
}

.register-agreement .modal_footer {
    height: 112px;
    padding: 0 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #dbe6f1;
}

.register-agreement .modal_btn {
    width: 216px;
    height: 72px;
    background: #3173C6;
    border-radius: 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}
</style> 