<template>
    <div>
        <div v-if="scanningPage" class="scanning-overlay" @click.stop></div>
        <Container :timeout="timeout">
            <template #head>
                <div class="ip_head">民事一审
                    <Steps :active="2" />
                </div>

            </template>
            <div class="con">
                <span v-if="!arriveDocumentPage && !scanDonePage">材料整理提示</span>
                <span v-else>{{ $store.getters.lafsName }}-{{ $store.getters.applyCaseTypeName}}</span>

                <div class="border">
                    <!-- 制作文书页面 -->
                    <template v-if="arriveDocumentPage">
                        <div class="arrive-document-page-container">
                            <div class="top-tip">
                                <img src="@/assets/danger.png" alt="">
                                <span>送达地址确认书可在线制作，其他材料需自行打印完后再扫描</span>
                            </div>
                            <table class="materials-needed-table">
                                <thead>
                                    <tr>
                                        <th>所需材料</th>
                                        <th>当事人身份证明</th>
                                        <th>委托代理人委托手续和身份材料</th>
                                        <th>证据目录及证据材料</th>
                                        <th>送达地址确认书</th>
                                        <th>其他材料</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>是否必要</td>
                                        <td class="required-item">是</td>
                                        <td :class="userType == applyUserType.BENREN ? '' : 'required-item'">
                                            {{ userType == applyUserType.BENREN ? '否' : '是' }}</td>
                                        <td class="required-item">是</td>
                                        <td class="required-item">是</td>
                                        <td>否</td>
                                    </tr>
                                </tbody>
                            </table>
                            <div class="page-main-title">送达地址确认书</div>
                            <div class="creation-choices">
                                <div class="choice-box active-choice">
                                    <img src="@/assets/add_arrive_img.png" @click="arriveDocumentMadeModalOpen = true"
                                        alt="">
                                </div>
                                <div class="choice-box" v-for="(item,index) in sdqrsImgList" :key="index">
                                    <img :src="item.path" alt="" style="height: 100%;" @click="previewImage(item.path)" />
                                </div>
                            </div>
                        </div>
                    </template>
                    <template v-else-if="scanStartPage">
                        <div class="scan-start-page">
                            <div>
                                <img src="@/assets/file_list.png" alt="" style="width: 710px;height: 390px;">
                                <div style="text-align: center;margin-top: 40px;">①根据指定的分类顺序摆放</div>
                            </div>
                            <div>
                                <img src="@/assets/file_start_scan.png" alt="">
                                <div style="text-align: center;margin-top: 40px;">②将材料反转扣入扫描口</div>
                            </div>
                        </div>
                    </template>
                    <template v-else-if="scanningPage">
                        <!-- <template v-else-if="false"> -->
                        <div class="scaning-page-container">
                            <div style="width:340px;height:352px;">
                                <div>
                                    <img src="@/assets/ai_logo_header.png" alt="" class="fading-logo-header">
                                </div>
                                <div>
                                    <img src="@/assets/ai_logo_body.png" alt="">
                                </div>
                            </div>
                            <div class="scanningText">
                                扫描中，已扫描<span
                                    class="imgSize">{{ isContinueScanningMode ? (scanIndex - existingImagesCount) : scanIndex }}</span>张，
                                已识别<span class="imgSize">{{ recognizedCount }}</span>张
                            </div>
                        </div>
                    </template>
                    <template v-else>
                        <div class="warning"><img src="@/assets/danger.png" alt="" />可选择图片进行再次分类确认</div>
                        <div class="imgContent">
                            <div v-for="(imgs,t) of typeMap" :key="t" class="type_item"
                                :class="{'required': requiredTypes.includes(t)}">
                                <div class="title" @click="$forceUpdate()">{{ t }}</div>
                                <img src="@/assets/pageUp.png" style="margin: 20px 0 10px 0"
                                    v-if="refMap[`imgList${t}`]&&refMap[`imgList${t}`].canScroll[0]"
                                    @touchstart="scroll(`imgList${t}`,-1)" @touchend="scrollEnd" @click="handleClick(`imgList${t}`,-1)" />
                                <img src="@/assets/pageUp_disable.png" style="margin: 20px 0 10px 0" v-else />
                                <div class="imgList_con">
                                    <div class="imgList" :ref="(el) => setRef(el,`imgList${t}`)"
                                        @scroll="canScroll(`imgList${t}`)">
                                        <img :src="img.path" class="imgItem" v-for="img of imgs" :key="img"
                                            @click="openCheckImgs(img)" />
                                    </div>
                                </div>
                                <img src="@/assets/pageDown.png" style="margin: 10px 0 20px 0"
                                    v-if="refMap[`imgList${t}`]&&refMap[`imgList${t}`].canScroll[1]"
                                    @touchstart="scroll(`imgList${t}`,1)" @touchend="scrollEnd" @click="handleClick(`imgList${t}`,1)" />
                                <img src="@/assets/pageDown_disable.png" style="margin: 10px 0 20px 0" v-else />
                            </div>
                        </div>
                    </template>
                </div>
                <div class="btns">
                    <template v-if="!arriveDocumentPage && scanStartPage">
                        <div class="ip_btn" @click="arriveDocumentPage = true">上一步</div>
                        <div class="ip_btn deep_btn" @click="startScanner">开始扫描</div>
                    </template>
                    <div class="ip_btn deep_btn" v-if="scanDonePage" @click="continueScanning">继续扫描</div>
                    <div class="ip_btn deep_btn" v-if="scanDonePage || arriveDocumentPage" @click="goNext">下一步</div>
                </div>
            </div>
        </Container>
        <a-modal v-model:open="checkDialogVisible" :footer="null" :closable="false" :header="null" width="1414px"
            :centered="true" wrapClassName="step3Dialog">
            <div class="step3Dialog_con">
                <div class="left_con">
                    <div class="con_title">
                        全部
                    </div>
                    <div class="all_img_con">
                        <img src="@/assets/pageUp.png" @touchstart="scroll('allImgList',-1)" @touchend="scrollEnd"
                            v-if="refMap[`allImgList`]&&refMap[`allImgList`].canScroll[0]" @click="handleClick('allImgList',-1)" />
                        <img src="@/assets/pageUp_disable.png" v-else />
                        <div class="all_img_list" :ref="(el) => setRef(el,`allImgList`)"
                            @scroll="canScroll(`allImgList`)">
                            <div v-for="(item, index) in imgList" :key="item.index" class="all_img_item"
                                :class="{ active: item.index === nowIndex }" @click="changeNowIndex(item.index,true)"
                                :style="colorStyle(item.type)">
                                {{item.type[0]}}<img :src="item.path" :ref="`all_item_${item.index}`" />
                                <div class="overlay" v-if="item.index === nowIndex">
                                    <span class="index-text">{{ index + 1 }}</span>
                                </div>
                            </div>
                        </div>
                        <img src="@/assets/pageDown.png" @touchstart="scroll('allImgList',1)" @touchend="scrollEnd"
                            v-if="refMap[`allImgList`]&&refMap[`allImgList`].canScroll[1]" @click="handleClick('allImgList',1)" />
                        <img src="@/assets/pageDown_disable.png" v-else />
                    </div>
                </div>
                <div class="right_con" v-if="currentImg">
                    <div class="con_title">
                        {{currentImg.type}}
                    </div>
                    <div class="con_main">
                        <div class="left_img_con">
                            <div class="img_con">
                                <img :src="currentImg.path" />
                            </div>
                            <div class="img_type">
                                <img src="@/assets/pageUp.png" style="transform: rotate(-90deg);"
                                    @touchstart="scroll('imgTypeList',-1,'left')" @touchend="scrollEnd"
                                    v-if="refMap[`imgTypeList`]&&refMap[`imgTypeList`].canScroll[0]" @click="handleClick('imgTypeList',-1,'left')" />
                                <img src="@/assets/pageUp_disable.png" style="transform: rotate(-90deg);" v-else />
                                <div class="img_type_list" :ref="(el) => setRef(el,`imgTypeList`)"
                                    @scroll="canScroll(`imgTypeList`)">
                                    <div class="img_type_item" v-for="item in typeMap[currentImg.type]"
                                        :key="item.index" @click="changeNowIndex(item.index)"
                                        :class="{ active: item.index === nowIndex }" :ref="`type_item_${item.index}`">
                                        <img :src="item.path" />
                                    </div>
                                </div>
                                <img src="@/assets/pageDown.png" style="transform: rotate(-90deg);"
                                    @touchstart="scroll('imgTypeList',1,'left')" @touchend="scrollEnd"
                                    v-if="refMap[`imgTypeList`]&&refMap[`imgTypeList`].canScroll[1]" @click="handleClick('imgTypeList',1,'left')" />
                                <img src="@/assets/pageDown_disable.png" style="transform: rotate(-90deg);" v-else />
                            </div>
                        </div>
                        <div class="right_img_con">
                            <div class="type_check_item" v-for="t of types" :key="t">
                                <img src="@/assets/sanjiao.png" v-if="t == currentImg.type" />
                                <div style="width:60px" v-else></div>
                                <div class="type_detail" :class="{active: t == currentImg.type}"
                                    @click="changeImgType(t)">
                                    <div :style="colorStyle(t)">{{t[0]}}</div>
                                    <div>{{requiredTypes.includes(t) ? '*' : ''}}{{t}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="con_footer">
                        <div class="modal_btn normal_btn" @click="deleteImg(currentImg)">
                            删除
                        </div>
                        <div class="modal_btn" @click="checkDialogVisible = false">
                            确定
                        </div>
                    </div>
                </div>
            </div>
        </a-modal>
        <ArriveDocumentMadeModal v-model:open="arriveDocumentMadeModalOpen" @updateFileImage="updateFileImage" />

        <!-- 图片预览模态框 -->
        <a-modal v-model:open="imagePreviewVisible" :footer="null" :closable="true" :width="'auto'" :centered="true"
            wrapClassName="image-preview-modal">
            <div class="image-preview-container">
                <img :src="previewImageUrl" alt="预览图片" class="preview-image" />
            </div>
        </a-modal>
    </div>
</template>

<script>
import Container from '@/components/Container.vue'
import { DQScanistor } from 'dq-eqc'
import Steps from '@/components/Steps.vue'
import imgRec from '@/utils/imgRec'
import ArriveDocumentMadeModal from '@/components/ArriveDocumentMadeModal.vue'
import { APPLY_USER_TYPE } from '@/utils/bizConstant'
import * as pdfjsLib from 'pdfjs-dist'
export default {
    components: {
        Container,
        Steps,
        ArriveDocumentMadeModal,
    },
    data() {
        return {
            timeout: 120,
            applyUserType: APPLY_USER_TYPE,
            oldImgList: [],
            imgList: [
                // { index: 0, path: 0, type: '当事人身份证明' },
                // { index: 1, path: 1, type: '当事人身份证明' },
                // { index: 2, path: 2, type: '当事人身份证明' },
                // { index: 3, path: 3, type: '当事人身份证明' },
                // { index: 4, path: 4, type: '委托代理人委托手续和身份材料' },
                // { index: 5, path: 4, type: '委托代理人委托手续和身份材料' },
                // { index: 6, path: 4, type: '委托代理人委托手续和身份材料' },
                // { index: 7, path: 4, type: '委托代理人委托手续和身份材料' },
                // { index: 8, path: 4, type: '委托代理人委托手续和身份材料' },
                // { index: 9, path: 4, type: '委托代理人委托手续和身份材料' },
            ],
            isScanning: false,
            types: [
                '当事人身份证明',
                '委托代理人委托手续和身份材料',
                '证据目录及证据材料',
                '送达地址确认书',
                '其他材料',
            ],
            colors: [
                '241, 160, 0',
                '32, 114, 254',
                '0, 190, 0',
                '254, 112, 13',
                '2, 187, 158',
            ],
            requiredTypes:
                this.$store.getters.applyUserType == APPLY_USER_TYPE.BENREN
                    ? ['当事人身份证明', '证据目录及证据材料', '送达地址确认书']
                    : [
                          '当事人身份证明',
                          '委托代理人委托手续和身份材料',
                          '证据目录及证据材料',
                          '送达地址确认书',
                      ],
            checkDialogVisible: false,
            nowIndex: 0,
            scanIndex: 0,
            recognizedCount: 0,
            existingImagesCount: 0,
            isContinueScanningMode: false,
            touchTimer: null,
            refMap: {},
            scannerEndFlag: false,
            arriveDocumentPage: true,
            arriveDocumentMadeModalOpen: false,

            sdqrsImgList: [],

            // 图片预览相关
            imagePreviewVisible: false,
            previewImageUrl: '',
            deletedImgIds: [], // 存储已删除的图片ID，用于后续同步到服务器
        }
    },
    watch: {
        imgList: {
            handler(val) {
                this.$nextTick(() => {
                    Object.keys(this.refMap).forEach((item) => {
                        this.canScroll(item)
                    })
                })
            },
            immediate: true,
        },
    },
    computed: {
        nowArrayIndex() {
            if (this.imgList.length === 0) return -1
            return this.imgList.findIndex(item => item.index === this.nowIndex)
        },
        currentImg() {
            if (this.nowArrayIndex === -1) {
                return null
            }
            return this.imgList[this.nowArrayIndex]
        },
        // 根据imgList的type，将其置入map[type]中，并且按照index排序
        typeMap() {
            let map = {}
            this.types.forEach((item) => {
                map[item] = []
            })
            this.imgList.forEach((item) => {
                map[item.type].push(item)
            })
            return map
        },

        scanStartPage() {
            return (
                this.imgList.length == 0 &&
                !this.isScanning &&
                !this.arriveDocumentPage
            )
        },
        scanningPage() {
            return this.isScanning && !this.arriveDocumentPage
        },
        timeout() {
            return this.scanningPage ? 0 : 120
        },
        scanDonePage() {
            return (
                this.imgList.length > 0 &&
                !this.isScanning &&
                !this.arriveDocumentPage
            )
        },
        userType() {
            return this.$store.getters.applyUserType
        },
    },
    methods: {
        startScanner(isContinue = false) {
            DQScanistor.open((res) => {
                // 开启扫描仪成功
                if (res.code == '201') {
                    this.isScanning = true
                    if (!isContinue) {
                        // 如果不是继续扫描，则重置索引和清空列表
                        this.scanIndex = 0
                        this.imgList = []
                        this.recognizedCount = 0 // 重置已识别计数
                        this.existingImagesCount = 0 // 重置已有图片计数
                        this.isContinueScanningMode = false // 重置继续扫描模式标志
                    } else {
                        // 继续扫描时，保留已有图片
                        this.existingImagesCount = this.imgList.length

                        // 确保新扫描的图片index值不会与已有图片重复
                        // 找到当前最大的index值
                        let maxIndex = -1
                        if (this.imgList && this.imgList.length > 0) {
                            this.imgList.forEach((img) => {
                                if (img.index > maxIndex) {
                                    maxIndex = img.index
                                }
                            })
                            // 设置scanIndex为最大index值+1，确保新图片不会与已有图片重复
                            this.scanIndex = maxIndex + 1
                        }
                    }
                }
                // 接收扫描仪Base64图片
                if (res.code == '200') {
                    let originImgBase64 = res.result
                    // 去除图片base64的data:image/jpg;base64,
                    let imgBase64 = originImgBase64.replace(
                        'data:image/jpg;base64,',
                        ''
                    )
                    // 先添加图片到列表，初始类型设为默认值，后续OCR完成后更新
                    let imgInfo = {
                        index: this.scanIndex++,
                        path: originImgBase64,
                        type: '其他材料', // 默认分类
                    }
                    this.imgList.push(imgInfo)
                    // 调用OCR进行内容识别和分类
                    this.dealOcr(imgBase64, imgInfo.index)
                }
                // 扫描结束
                if (res.code == '202') {
                    this.scannerEndFlag = true
                    // 关闭扫描，但不立即设置isScanning = false
                    // 等待所有OCR识别完成后再设置状态
                    this.checkAllRecognized()
                }
                // 扫描仪卡纸异常
                if (res.code == '203') {
                    this.$messageConfirm({
                        message: '扫描仪卡纸，请将材料拿出重新扫码',
                        confirmText: '重新扫描',
                    }).then(() => {
                        this.isScanning = false
                    })
                }
                // 扫描仪异常
                if (res.code == '204') {
                    this.$messageConfirm({
                        message: '扫描仪异常，请联系管理人员',
                        confirmText: '确定',
                    }).then(() => {
                        this.isScanning = false
                    })
                }
                // 扫描仪无纸异常
                if (res.code == '205') {
                    this.$messageConfirm({
                        message: '扫描仪无纸，请将材料放入扫描仪',
                        confirmText: '确定',
                    }).then(() => {
                        this.isScanning = false
                    })
                }
            })
        },
        dealOcr(imgBase64, imgIndex) {
            this.$http
                .post('/api/ocr', {
                    pic: imgBase64,
                })
                .then((res) => {
                    if (res.rec_text) {
                        let imageTypeResult = imgRec(res.rec_text)
                        let category = imageTypeResult.category

                        // 处理特殊分类名称映射
                        if (category.includes('身份证明')) {
                            // 身份证明需要进一步细分为当事人或代理人
                            // if (
                            //     this.$store.getters.applyUserType ==
                            //         this.applyUserType.BENREN &&
                            //     res.rec_text.includes(
                            //         this.$store.getters.currentUserInfo.username
                            //     )
                            // ) {
                            //     category = '当事人身份证明'
                            // } else if (
                            //     this.$store.getters.applyUserType ==
                            //         this.applyUserType.TAREN &&
                            //     res.rec_text.includes(
                            //         this.$store.getters.currentUserInfo.username
                            //     )
                            // ) {
                            //     category = '委托代理人委托手续和身份材料'
                            // }

                            // 目前由于OCR识别姓名的准确率较低，所以暂时先归类为当事人身份证明
                            category = '当事人身份证明'
                        } else if (this.types.includes(category)) {
                            // 如果是其他预定义类型之一，保留原类型
                            // 无需额外处理
                        } else {
                            // 只有当分类结果不在预定义类型中时，才归类为"其他材料"
                            category = '其他材料'
                        }

                        this.updateImageType(imgIndex, category)
                        // 根据置信度决定是否自动分类
                        if (imageTypeResult.confidence < 0.5) {
                            console.log(
                                `图片${imgIndex}的识别置信度较低(${imageTypeResult.confidence})，建议人工确认`
                            )
                        }

                        // 增加已识别计数
                        this.recognizedCount++
                        // 检查是否所有图片都已识别
                        this.checkAllRecognized()
                    } else {
                        console.log('空白页')
                        // 即使识别失败也计数，表示处理完成
                        this.recognizedCount++
                        // 检查是否所有图片都已识别
                        this.checkAllRecognized()
                    }
                })
                .catch((err) => {
                    console.log('空白页:', err)
                    // 识别失败也算作已处理
                    this.recognizedCount++
                    // 检查是否所有图片都已识别
                    this.checkAllRecognized()
                })
        },
        // 更新图片类型的辅助方法
        updateImageType(imgIndex, newType) {
            const imgToUpdate = this.imgList.find(
                (img) => img.index === imgIndex
            )
            if (imgToUpdate) {
                imgToUpdate.type = newType
            }
        },
        // 动态设置ref，并且设置完成后判断是否能滚动
        setRef(el, refName) {
            if (!this.refMap[refName]) {
                this.refMap[refName] = {
                    ref: el,
                    canScroll: [false, false],
                }
            } else {
                this.refMap[refName].ref = el
            }
            let self = this
            setTimeout(() => {
                self.canScroll(refName)
            }, 150)
            return refName
        },
        async goNext() {
            if (this.arriveDocumentPage) {
                this.arriveDocumentPage = false
                return
            }
            // 检查必需的文档类型是否都已上传
            const missingTypes = this.checkRequiredDocuments()
            if (missingTypes.length > 0) {
                this.$messageConfirm({
                    message: `请先上传以下必需文档：${missingTypes.join('、')}`,
                    confirmText: '确定',
                })
                return
            }

            // 先处理需要删除的文件列表(1.已经上传的 && 分类发生变化的)
            await this.excuteDeleteImg()
            // 整理出文件列表中需要上传的文件(已经上传改了分类的和新上传的；)
            let needUploadImgs = this.imgList.filter(item => item.changed || !item.needConversion)
            if(needUploadImgs.length <= 0) {
                this.$router.push('/step4')
                return
            }
            // 使用Promise.all等待所有上传完成
            const uploadPromises = needUploadImgs.map((item) =>
                this.uploadImage(item)
            )
            const results = await Promise.all(uploadPromises)

            // 检查是否所有上传都成功
            if (results.includes(false)) {
                this.$messageConfirm({
                    message: '部分材料上传失败，请重试',
                    confirmText: '确定',
                })
                return
            }
            // 全部上传成功且获取了立案预约信息，跳转到下一页
            this.$router.push('/step4')
        },
        // 找到需要删除的图片并删除
        async excuteDeleteImg() {
            // 整理出oldImgList需要调用接口删除的图片
            let deleteImgIdList = []
            // 检测送达地址有没有被更换分类，如果更换就删除
            let oldSddzImgList = this.imgList.filter(item => !!item.noUpload)
            oldSddzImgList.forEach(item => {
                if(item.type != '送达地址确认书') {
                    deleteImgIdList.push(item.id)
                    let newItem = this.imgList.find(newImg => newImg.id == item.id)
                    if(newItem) {
                        newItem.changed = true
                    }
                }
            })

            this.oldImgList.forEach((oldImg) => {
                this.imgList
                    .filter((img) => !!img.needConversion)
                    .forEach((newImg) => {
                        if (
                            oldImg.id == newImg.id &&
                            oldImg.type != newImg.type
                        ) {
                            deleteImgIdList.push(oldImg.id)
                            newImg.changed = true
                            if(newImg.extname == 'pdf') {
                                delete newImg.extname
                            }
                        }
                    })
            })
            
            // 等待所有删除完成
            await Promise.all(
                deleteImgIdList.map((imgId) =>
                    this.$rpa.dealFetch(
                        `https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/ssclfj/delete`,
                        {
                            method: 'POST',
                            body: {
                                layyid: this.$store.getters.layyId,
                                id: imgId,
                            },
                        }
                    )
                )
            )
        },
        // 检查必需文档是否都已上传
        checkRequiredDocuments() {
            const missingTypes = []
            this.requiredTypes.forEach((type) => {
                if (!this.typeMap[type] || this.typeMap[type].length === 0) {
                    missingTypes.push(type)
                }
            })
            return missingTypes
        },
        colorStyle(type) {
            return {
                background: `rgba(${
                    this.colors[this.types.indexOf(type)]
                },0.1)`,
                color: `rgba(${this.colors[this.types.indexOf(type)]},1)`,
            }
        },
        openCheckImgs(img) {
            this.checkDialogVisible = true
            this.nowIndex = img.index
        },
        async changeNowIndex(index, notScroll) {
            this.nowIndex = index
            await this.$nextTick()
            await this.$nextTick()
            if (this.$refs[`all_item_${index}`] && this.$refs[`all_item_${index}`][0]) {
                this.$refs[`all_item_${index}`][0].scrollIntoView()
            }
            if (this.$refs[`type_item_${index}`] && this.$refs[`type_item_${index}`][0]) {
                this.$refs[`type_item_${index}`][0].scrollIntoView()
            }
        },

        /**
         * 处理点击事件，滚动半页
         */
        handleClick(ref, count, type) {
            let dom = this.refMap[ref].ref
            if (!dom) return
            
            // 计算应该滚动的距离（半页）
            let scrollAmount
            if (!type || type === 'top') {
                // 垂直滚动
                scrollAmount = dom.clientHeight / 2 * count
                dom.scrollBy({
                    top: scrollAmount,
                    behavior: 'smooth'
                })
            } else {
                // 水平滚动
                scrollAmount = dom.clientWidth / 2 * count
                dom.scrollBy({
                    left: scrollAmount,
                    behavior: 'smooth'
                })
            }
        },
        /**
         * 实际滚动(方向)
         */
        scroll(ref, count, type) {
            let dom = this.refMap[ref].ref
            console.log(this.refMap[ref])
            this.touchTimer = setInterval(() => {
                let param = { behavior: 'smooth' }
                if (!type) type = 'top'
                param[type] = 10 * count
                dom.scrollBy(param)
            }, 50) // 每200ms滚动一次
        },
        scrollEnd() {
            clearInterval(this.touchTimer)
        },
        // 检查是否可以滚动
        canScroll(t) {
            if (!this.refMap[t] && !this.refMap[t].ref) return
            let container = this.refMap[t].ref
            if (!container) return
            if (container.scrollTop > 0 || container.scrollLeft > 0) {
                this.refMap[t].canScroll[0] = true
            } else {
                this.refMap[t].canScroll[0] = false
            }
            if (
                container.scrollTop <
                    container.scrollHeight - container.clientHeight ||
                container.scrollLeft <
                    container.scrollWidth - container.clientWidth
            ) {
                this.refMap[t].canScroll[1] = true
            } else {
                this.refMap[t].canScroll[1] = false
            }
        },
        // 继续扫描功能
        continueScanning() {
            // 关闭当前扫描仪连接，然后以继续扫描模式重新启动
            DQScanistor.close(() => {
                setTimeout(() => {
                    // 重置识别计数器，但不清空已有图片
                    this.recognizedCount = 0
                    // 记录当前已有图片数量
                    this.existingImagesCount = this.imgList.length
                    // 设置为继续扫描模式
                    this.isContinueScanningMode = true
                    // 传入true表示继续扫描模式，不清空已有图片
                    this.startScanner(true)
                }, 500)
            })
        },
        resetScanner() {
            // 添加重新扫描前的确认
            this.$messageConfirm({
                message: '重新扫描将清空当前已扫描的文件，确定要继续吗？',
                showCancel: true,
                confirmText: '确定',
                cancelText: '取消',
            })
                .then(() => {
                    // 用户确认后清空数据并重置扫描状态
                    this.imgList = []
                    this.isScanning = false
                    this.scanIndex = 0
                    this.recognizedCount = 0 // 重置已识别计数
                    this.isContinueScanningMode = false // 重置继续扫描模式标志

                    // 关闭扫描仪，然后重新启动
                    DQScanistor.close(() => {
                        setTimeout(() => {
                            this.startScanner()
                        }, 500) // 短暂延迟后重新启动扫描
                    })
                })
                .catch(() => {
                    // 用户取消，不执行任何操作
                })
        },
        // 添加一个方法来检查是否所有图片都已识别
        checkAllRecognized() {
            // 如果已设置扫描结束标志，并且当前批次的图片都已识别完成
            if (
                this.scannerEndFlag &&
                this.recognizedCount >=
                    this.imgList.length - this.existingImagesCount
            ) {
                // 所有图片都已识别完成，可以结束扫描状态
                this.isScanning = false
                this.scannerEndFlag = false // 重置标志
                // 添加制作的送达地址确认书
                this.sdqrsImgList.forEach((item) => {
                    if(this.imgList.some(img => img.id == item.id)) {
                        return
                    }
                    this.imgList.push({
                        id: item.id,
                        index: this.scanIndex++,
                        path: item.path,
                        type: '送达地址确认书',
                        noUpload: true,
                        needConversion: true,
                    })
                })
            } else if (this.scannerEndFlag) {
                // 如果扫描已结束但识别未完成，设置延时检查
                setTimeout(() => {
                    this.checkAllRecognized()
                }, 500) // 每500ms检查一次
            }
        },
        async updateFileImage(pdfFile) {
            const imageBase64 = await this.convertPdfUrlToBase64Image(pdfFile.url)
            pdfFile.path = imageBase64
            this.sdqrsImgList.push(pdfFile)
        },
        // 图片预览方法
        previewImage(imageUrl) {
            this.previewImageUrl = imageUrl
            this.imagePreviewVisible = true
        },
        // 获取立案预约信息
        async getLayyDetail() {
            debugger
            let layyInfoRes = await this.$rpa.dealFetch(
                `https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/layyxq/${this.$store.getters.layyId}/0`,
                { method: 'GET' },
                false // 禁用loading
            )
            debugger
            if (layyInfoRes.code === 200 && layyInfoRes.data) {
                // 将立案预约信息存入store
                this.$store.dispatch('layy/setLayyInfo', layyInfoRes.data)
                this.$store.dispatch(
                    'layy/setApplyUserType',
                    layyInfoRes.data.layy.sqrsf
                )
                // 等待formatImgList完成
                this.oldImgList = await this.formatImgList(layyInfoRes.data)
                if(this.oldImgList.length <= 0) {
                    this.arriveDocumentPage = true
                } else {
                    this.arriveDocumentPage = false
                }
                this.imgList = JSON.parse(JSON.stringify(this.oldImgList))
                debugger
                this.$store.dispatch('loading/setLoading', false)
                return true
            }
        },
        // 将返回的案件材料数据组装为imgList格式
        async formatImgList(data) {
            debugger
            let imgList = []
            let index = 0
            // 检查是否有案件材料数据
            if (data && data.sscls && data.sscls.length > 0) {
                // 遍历所有材料类型
                data.sscls.forEach((sscl) => {
                    // 确保该类型有文件且类型在预定义的types中
                    if (
                        sscl.wjs &&
                        sscl.wjs.length > 0 &&
                        this.types.includes(sscl.clmc)
                    ) {
                        // 遍历该类型下的所有文件
                        sscl.wjs.forEach((wj) => {
                            // 需要将URL转换为Base64，这里先将它添加到列表
                            // 稍后我们会加载图片并转换为Base64
                            imgList.push({
                                id: wj.id,
                                extname: wj.extname,
                                index: index++,
                                path: wj.url, // 先保存URL，稍后转换为Base64
                                type: sscl.clmc, // 分类名称
                                needConversion: true, // 标记需要转换
                            })
                        })
                    }
                })

                // 等待所有图片URL转换为Base64
                debugger
                await this.convertImagesUrlToBase64(imgList)
                debugger
            }

            return imgList
        },

        // 将图片URL转换为Base64
        async convertImagesUrlToBase64(imgList) {
            const conversionPromises = imgList.map((img, index) => {
                if (img.needConversion) {
                    return new Promise((resolve, reject) => {
                        // 使用fetch获取图片
                        fetch(img.path)
                            .then((response) => {
                                if (!response.ok) {
                                    throw new Error(
                                        `HTTP error! status: ${response.status}`
                                    )
                                }
                                return response.blob()
                            })
                            .then(async (blob) => {
                                if (img.extname == 'pdf') {
                                    img.path = await this.convertPdfUrlToBase64Image(img.path)
                                    delete img.extname
                                    resolve()
                                } else {
                                    const reader = new FileReader()
                                    reader.onloadend = () => {
                                        // 替换原始URL为Base64
                                        img.path = reader.result
                                        console.log(
                                            `图片 ${index} 已转换为Base64`
                                        )
                                        resolve()
                                    }
                                    reader.onerror = reject
                                    reader.readAsDataURL(blob)
                                }
                            })
                            .catch((error) => {
                                console.error(
                                    `无法转换图片 ${img.path} 为Base64:`,
                                    error
                                )
                                // 即使转换失败，也resolve，避免阻塞其他图片
                                resolve()
                            })
                    })
                }
                return Promise.resolve() // 对于不需要转换的图片，直接返回一个resolved promise
            })

            await Promise.all(conversionPromises)
        },
        // 图片上传
        async uploadImage(img) {
            try {
                let ssclTypeList = []
                if (
                    this.$store.getters.ajlxInfo &&
                    this.$store.getters.ajlxInfo.sscl
                ) {
                    ssclTypeList = this.$store.getters.ajlxInfo.sscl
                }
                // 如果案件基本信息拿不到，去立案预约信息中获取
                if (!ssclTypeList || ssclTypeList.length == 0) {
                    ssclTypeList = this.$store.getters.layyInfo.sscls.map(
                        (item) => {
                            return {
                                name: item.clmc,
                                cllx: item.cllx,
                            }
                        }
                    )
                }
                // 1. 获取签名
                let param1 = {
                    cllx: ssclTypeList.find((item) => item.name == img.type)
                        .cllx,
                    fydm: this.$store.getters.fyId,
                    ext: '.jpg',
                    path: 'layy',
                }
                let res1 = await this.$rpa.dealFetch(
                    `https://zxfw.court.gov.cn/yzw/yzw-zxfw-ajfw/api/v1/file/upload/signature`,
                    {
                        method: 'POST',
                        body: param1,
                    }
                )
                if (res1.code != 200) {
                    console.error('图片上传获取签名失败')
                    return false
                }

                // 2. 前置上传
                let signatureInfo = res1.data
                // 创建FormData对象
                const formData = new FormData()
                // 添加签名相关字段
                formData.append('key', signatureInfo.storeAs)
                formData.append('x-oss-security-token', signatureInfo.token)
                formData.append('policy', signatureInfo.policy)
                formData.append('OSSAccessKeyId', signatureInfo.ossaccessKeyId)
                formData.append('success_action_status', '200')
                formData.append('signature', signatureInfo.signature)
                // 将Base64图片转换为Blob对象
                const byteString = atob(img.path.split(',')[1])
                const mimeString = img.path
                    .split(',')[0]
                    .split(':')[1]
                    .split(';')[0]
                const ab = new ArrayBuffer(byteString.length)
                const ia = new Uint8Array(ab)
                for (let i = 0; i < byteString.length; i++) {
                    ia[i] = byteString.charCodeAt(i)
                }
                const blob = new Blob([ab], { type: mimeString })
                // 添加文件
                formData.append('file', blob)
                // 发送请求
                const res2 = await this.$rpa.dealFetch(
                    'https://zxfy2-oss.oss-cn-north-2-gov-1.aliyuncs.com/',
                    {
                        method: 'POST',
                        body: formData,
                    }
                )
                // 检查上传结果
                if (!res2.success) {
                    console.error('图片上传失败')
                    return false
                }
                // 获取当前材料类型的序号
                const typeItems = this.typeMap[img.type] || []
                const typeIndex = typeItems.findIndex(
                    (item) => item.index === img.index
                )

                // 生成文件名: 如果是第一个文件，则不显示序号；从第二个文件开始显示序号
                const displayIndex = typeIndex > 0 ? typeIndex + 1 : ''
                const fileName = `${img.type}${displayIndex}.jpg`

                // 3. 上传材料
                let param3 = {
                    wjbh: signatureInfo.storeAs,
                    layyid: this.$store.getters.layyId,
                    fyId: this.$store.getters.fyId,
                    xh: typeIndex + 1, // 序号从1开始
                    wjmc: fileName,
                    path: signatureInfo.storeAs,
                    ssclid: this.getSsclId(param1.cllx),
                    cllx: param1.cllx,
                    clmc: img.type,
                    bccl: null,
                    name: fileName,
                    extname: 'jpg',
                    url: 'blob:https://zxfw.court.gov.cn/123', // 可以随便传
                }

                let res3 = await this.$rpa.dealFetch(
                    `https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/ssclfj`,
                    {
                        method: 'POST',
                        body: param3,
                    }
                )
                if (res3.code !== 200) {
                    console.error(`${img.type}材料信息上传失败`)
                    return false
                }

                return true
            } catch (error) {
                console.error('上传过程出错:', error)
                return false
            }
        },
        // 安全获取ssclid的方法
        getSsclId(cllx) {
            // 检查layyInfo是否存在
            if (!this.$store.getters.layyInfo) {
                console.error('layyInfo为空，请先获取立案预约信息')
                return ''
            }

            // 检查sscls是否存在
            if (
                !this.$store.getters.layyInfo.sscls ||
                !Array.isArray(this.$store.getters.layyInfo.sscls)
            ) {
                console.error('sscls不存在或不是数组')
                return ''
            }

            // 查找匹配的项
            const matchedItem = this.$store.getters.layyInfo.sscls.find(
                (item) => item.cllx == cllx
            )
            return matchedItem ? matchedItem.id : ''
        },
        async convertPdfUrlToBase64Image(url) {
            const loadingTask = pdfjsLib.getDocument(url)
            const pdf = await loadingTask.promise
            const page = await pdf.getPage(1)
            const viewport = page.getViewport({ scale: 2.0 })

            const canvas = document.createElement('canvas')
            const context = canvas.getContext('2d')
            canvas.height = viewport.height
            canvas.width = viewport.width

            const renderContext = {
                canvasContext: context,
                viewport: viewport,
            }

            await page.render(renderContext).promise

            return canvas.toDataURL('image/jpeg')
        },

        deleteImg(img) {
            this.$messageConfirm({
                message: '确定要删除该图片吗？',
                showCancel: true,
                confirmText: '确定',
                cancelText: '取消',
            }).then(async () => {
                // 如果是已上传到服务器的图片，先调用删除接口
                if (img.id) {
                    try {
                        // 显示加载状态
                        this.$store.dispatch('loading/showLoading', '删除中...');
                        
                        // 调用删除接口
                        const result = await this.$rpa.dealFetch(
                            `https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/ssclfj/delete`,
                            {
                                method: 'POST',
                                body: {
                                    layyid: this.$store.getters.layyId,
                                    id: img.id,
                                },
                            }
                        );
                        
                        // 隐藏加载状态
                        this.$store.dispatch('loading/hideLoading');
                        
                        // 检查删除结果
                        if (result.code !== 200) {
                            this.$messageConfirm({
                                message: '删除失败，请重试',
                                confirmText: '确定',
                            });
                            return;
                        }
                        
                        // 删除成功，将ID添加到已删除列表
                        if (!this.deletedImgIds) {
                            this.deletedImgIds = [];
                        }
                        this.deletedImgIds.push(img.id);
                    } catch (error) {
                        // 隐藏加载状态
                        this.$store.dispatch('loading/hideLoading');
                        
                        console.error('删除图片失败:', error);
                        this.$messageConfirm({
                            message: '删除失败，请重试',
                            confirmText: '确定',
                        });
                        return;
                    }
                }
                
                // 找到要删除图片的索引位置
                const deleteIndex = this.imgList.findIndex(item => item.index === img.index);
                if (deleteIndex === -1) return;
                
                // 删除图片
                this.imgList.splice(deleteIndex, 1);
                
                // 处理删除后的情况
                if (this.imgList.length === 0) {
                    // 如果删除后没有图片了，关闭对话框
                    this.checkDialogVisible = false;
                    return;
                }
                
                // 如果删除的是当前显示的图片，需要调整当前显示的图片索引
                if (this.imgList.length > 0) {
                    if (deleteIndex >= this.imgList.length) {
                        // 如果删除的是最后一张图片，显示新的最后一张
                        this.changeNowIndex(this.imgList[this.imgList.length - 1].index);
                    } else {
                        // 否则显示下一张图片
                        this.changeNowIndex(this.imgList[deleteIndex].index);
                    }
                }
                
                // 更新UI
                this.$nextTick(() => {
                    Object.keys(this.refMap).forEach((item) => {
                        this.canScroll(item);
                    });
                });
            }).catch(() => {
                // 用户取消删除操作
            });
        },
        changeImgType(newType) {
            if (this.currentImg) {
                this.currentImg.type = newType;
            }
        },
    },
    mounted() {},
    created() {
    debugger
        this.$store.dispatch('loading/setLoading', true)
        pdfjsLib.GlobalWorkerOptions.workerSrc = new URL(`../../utils/pdf.worker.min.js`,import.meta.url).toString()
        this.getLayyDetail()
    },
}
</script>
<style scoped>
.scanningText {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #333333;
    height: 48px;
    line-height: 48px;
    margin-top: 30px;
}
.imgSize {
    font-size: 48px;
    color: #0b7cff;
    margin: 0 10px;
}
.warning {
    height: 20px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 20px;
    color: #c67b31;
    align-self: flex-start;
    display: flex;
    align-items: center;
}
.warning img {
    margin-right: 5px;
}

.imgContent {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-top: 24px;
}

.type_item {
    width: 320px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.type_item .title {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 24px;
    color: #333333;
    white-space: nowrap;
}
.required .title::before {
    content: '*';
    margin-right: 4px;
}

.imgList_con {
    width: 320px;
    height: 343px;
    background: #ffffff;
    border: 1px solid #a1b1c5;
    padding: 13px;
}

.imgList {
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    overflow: auto;
}

.imgList::-webkit-scrollbar {
    display: none;
}

.imgItem {
    width: 130px;
    height: 160px;
    background: #c2c2c2;
    margin: 5px;
}
</style>

<style scoped>
.step3Dialog_con {
    width: 100%;
    height: 940px;
    display: flex;
    justify-content: space-between;
    overflow: hidden;
}

.step3Dialog_con .left_con {
    width: 146px;
    height: 100%;
    background: #ffffff;
    border-radius: 20px 20px 20px 20px;
    overflow: hidden;
}

.step3Dialog_con .right_con {
    width: 1210px;
    height: 100%;
    background: #ffffff;
    border-radius: 20px 20px 20px 20px;
    overflow: hidden;
}

.step3Dialog_con .con_title {
    height: 100px;
    width: 100%;
    background: #dbe6f1;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 42px;
    color: #03206a;
    display: flex;
    align-items: center;
    justify-content: center;
}

.con_main {
    width: 100%;
    height: 728px;
    padding: 20px 50px;
    display: flex;
    justify-content: space-between;
}

.left_img_con {
    width: 461px;
}

.img_con {
    width: 461px;
    height: 563px;
    padding: 10px;
    border: 1px solid #a1b1c5;
}
.img_con img {
    width: 100%;
    height: 100%;
    background: #c2c2c2;
}

.img_type {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 461px;
    margin-top: 20px;
}

.img_type_list {
    width: 360px;
    height: 105px;
    display: flex;
    white-space: nowrap;
    align-items: center;
    overflow: auto;
}

.img_type_list::-webkit-scrollbar {
    display: none;
}

.img_type_item {
    flex: 0 0 auto;
    width: 86px;
    height: 105px;
    border: 1px dashed transparent;
    margin-right: 5px;
    padding: 3px;
}

.img_type_item:last-of-type {
    margin-right: 0;
}

.img_type_item.active {
    border: 1px dashed #3173c6;
}

.img_type_item img {
    background: #c2c2c2;
    width: 100%;
    height: 100%;
}

.all_img_con {
    height: calc(100% - 100px);
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
}

.all_img_list {
    height: 700px;
    width: 100%;
    overflow: auto;
}

.all_img_list::-webkit-scrollbar {
    display: none;
}

.all_img_item {
    height: 105px;

    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    border: 2px solid transparent;
    position: relative;
}

.all_img_item.active {
    border: 2px solid #3173c6 !important;
}

.all_img_item img {
    width: 80px;
    height: 99px;
    background: #c2c2c2;
    margin-left: 2px;
}

/* 遮罩层样式 */
.overlay {
    position: absolute;
    top: 1px;
    right: 2px;
    width: 80px;
    height: 99px;
    background-color: rgba(0, 0, 0, 0.5); /* 半透明黑色遮罩 */
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 居中显示的索引文字 */
.index-text {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 24px;
    color: #ffffff;
}

.step3Dialog_con .con_title .title {
    font-size: 42px;
    font-weight: bold;
    color: #03206a;
}

.step3Dialog_con .con_title .title::before {
    content: '';
    width: 10px;
    height: 40px;
    background: #03206a;
}

.right_img_con {
    width: 625px;
    height: 563px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.right_img_con .type_check_item {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.right_img_con .type_check_item .type_detail {
    width: 556px;
    height: 72px;
    border: 1px solid #a1b1c5;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 32px;
    color: #78808a;
    text-indent: 20px;
    line-height: 69px; /**字体上下有一定偏移，观感偏下 */
    display: flex;
}
/**除最后一个div，其他div底部边框设置none
 */
.right_img_con .type_check_item:not(:last-child) .type_detail {
    border-bottom: none;
}

.type_detail > div:first-child {
    width: 25px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-indent: 0;
}

.type_detail > div:last-child {
    flex: 1;
}

.type_detail.active > div:last-child {
    background: #3173c6;
    color: #fff;
}

.step3Dialog_con .con_footer {
    border-top: 2px solid #dbe6f1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 112px;
    padding: 0 50px;
}

.step3Dialog_con .modal_btn {
    width: 216px;
    height: 72px;
    background: #3173c6;
    border-radius: 10px 10px 10px 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20px;
    border: 1px solid #3173c6;
}
.normal_btn {
    background: #fff !important;
    color: #3173c6 !important;
}
</style>
<style scoped>
.con {
    display: flex;
    align-items: center;
    height: 100%;
    flex-direction: column;
    padding: 20px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 30px;
    color: #333333;
    font-weight: bold;
}

.border {
    width: 100%;
    height: 564px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #a1b1c5;
    margin-top: 20px;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 10px 30px;
}

.ip_head {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 32px;
    color: #333333;
}

.btns {
    display: flex;
    justify-content: center;
    width: 100%;
    position: relative;
}

.ip_btn {
    width: 216px;
    height: 72px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #3173c6;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #3173c6;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20px 12px;
}

.deep_btn {
    background: #3173c6;
    color: #ffffff;
}
</style>

<style>
.step3Dialog .ant-modal-header {
    display: none;
}

.step3Dialog .ant-modal-content {
    background: transparent !important;
    box-shadow: none !important;
}

/* 图片预览模态框样式 */
.image-preview-modal .ant-modal-content {
    background: rgba(0, 0, 0, 0.85) !important;
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.image-preview-modal .ant-modal-close {
    color: white;
}

.image-preview-modal .ant-modal-close:hover {
    color: #3173c6;
}

.image-preview-modal .ant-modal-body {
    padding: 0;
}
</style>

<style scoped>
.scan-button {
    width: 216px;
    height: 72px;
    background: #3173c6;
    color: #ffffff;
    border-radius: 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin: 200px auto;
}
.scan-button:hover {
    background: #4285d8;
}
.scan-button:active {
    background: #2864b7;
}

.arrive-document-page-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 230px;
    font-family: Source Han Sans CN, Source Han Sans CN;
}

.scaning-page-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.top-tip {
    display: flex;
    align-items: center;
    color: #c67b31;
    font-size: 18px;
    font-weight: 400;
}

.top-tip img {
    height: 20px;
    width: auto;
    margin-right: 8px;
}

.materials-needed-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    font-size: 18px;
}

.materials-needed-table th,
.materials-needed-table td {
    border: 1px solid #dbe6f1;
    padding: 15px 10px;
    text-align: center;
    font-weight: 400;
}

.materials-needed-table thead th {
    color: #333333;
    background: #fff;
    font-weight: 500;
}

.materials-needed-table tbody tr {
    background: #ffffff;
}

.materials-needed-table tbody td {
    color: #333333;
}

.materials-needed-table th:first-child,
.materials-needed-table td:first-child {
    background: #dbe6f1;
    font-weight: bold;
}

.materials-needed-table .required-item {
    color: #ff0000;
}

.page-main-title {
    font-size: 32px;
    font-weight: 500;
    color: #333333;
    margin: 40px 0 30px 0;
}

.creation-choices {
    display: flex;
    gap: 30px;
    width: 100%;
    overflow-x: auto;
}

.choice-box {
    width: 142px;
    height: 172px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 1px solid #c2c2c2;
    background: #c2c2c2;
}

.choice-box p {
    color: #333;
    font-size: 18px;
    margin-top: 15px;
    font-weight: 400;
}

.active-choice {
    cursor: pointer;
}

.disabled-choice {
    background: #e5e5e5;
}

.icon-wrapper {
    width: 80px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.online-creation-icon-view {
    width: 70px;
    height: 45px;
    background-color: #3389ff;
    border-radius: 5px;
    position: relative;
    border: 2px solid #2367c8;
}

.online-creation-icon-plus-view {
    position: absolute;
    right: 5px;
    bottom: 5px;
    width: 20px;
    height: 20px;
    background-color: #00d588;
    border-radius: 50%;
    color: white;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    border: 1px solid white;
}

.scan-start-page {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    padding: 20px 0px;
    padding-left: 100px;
    padding-right: 230px;
}

.scan-start-page > div {
    text-align: center;
    font-weight: 400;
    font-size: 32px;
    color: #333333;
}

.fading-logo-header {
    float: right;
    animation: fadeInOut 2s ease-in-out infinite;
}

@keyframes fadeInOut {
    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0;
    }
}

/* 图片预览样式 */
.image-preview-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
}

.preview-image {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

.scanning-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0);
    z-index: 99;
}
</style>