// imgRecExample.js - Node.js环境下使用imgRec的示例

// 导入imgRec函数
const imgRec = require('../utils/imgRec');

/**
 * 示例函数：使用imgRec对文本进行分类
 * @param {string} text - 需要分类的文本内容
 */
function classifyText(text) {
  // 调用imgRec函数进行文本分类
  const result = imgRec(text);
  
  // 输出分类结果
  console.log('分类结果：', result.category);
  console.log('置信度：', (result.confidence * 100).toFixed(2) + '%');
  
  // 输出所有类别的置信度
  console.log('\n所有类别的置信度:');
  for (const category in result.allConfidences) {
    const confidenceValue = (result.allConfidences[category] * 100).toFixed(2) + '%';
    console.log(`- ${category}: ${confidenceValue}`);
  }
  
  console.log('\n文本内容：', result.text);
  
  return result;
}

// 测试示例
function runExamples() {
  console.log('===== 示例1：身份证文本 =====');
  classifyText('居民身份证 姓名：张三 性别：男 民族：汉 出生：1990年01月01日 住址：北京市海淀区');
  
  console.log('\n===== 示例2：起诉状文本 =====');
  classifyText('起诉状 原告：李四，男，汉族 被告：王五，男，汉族 诉讼请求：1.判令被告支付货款10000元 事实与理由：原告与被告于2023年签订合同');
  
  console.log('\n===== 示例3：送达地址确认书 =====');
  classifyText('送达地址确认书 送达方式：邮寄 送达地址：北京市朝阳区 电话：13800138000 邮政编码：100000');
}

// 导出函数供其他模块使用
module.exports = {
  classifyText
};

// 如果直接运行此文件，则执行示例
if (require.main === module) {
  runExamples();
} 