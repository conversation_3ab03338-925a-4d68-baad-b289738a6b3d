<template>
    <Modal :open="open" @update:open="$emit('update:open', $event)" :title="title || '提示'" :width="width"
        :centered="true" :maskClosable="false" :closable="false" :footer="null" :transitionName="'fade'"
        :maskTransitionName="'fade'" :wrapClassName="'confirm-dialog ' + modalClass">
        <div class="confirm-content">
            <div class="image-container" v-if="image">
                <img :src="image" class="modal-image" />
            </div>
            <div :class="['message-container', {'with-image': image}]" v-if="message">
                <div v-html="message"></div>
            </div>
        </div>
        <div class="confirm-footer">
            <div class="confirm-btn cancel-btn" @click="handleCancel" v-if="showCancel">
                {{ cancelText || '取消' }}
            </div>
            <div class="confirm-btn confirm-btn-primary" @click="handleConfirm">
                {{ confirmText || '确定' }}
            </div>
        </div>
    </Modal>
</template>

<script>
import { Modal } from 'ant-design-vue'

export default {
    name: 'ConfirmModal',
    components: {
        Modal,
    },
    props: {
        open: {
            type: Boolean,
            required: true,
        },
        title: {
            type: String,
            default: '',
        },
        message: {
            type: String,
            default: '',
        },
        cancelText: {
            type: String,
            default: '',
        },
        confirmText: {
            type: String,
            default: '',
        },
        showCancel: {
            type: Boolean,
            default: false,
        },
        image: {
            type: String,
            default: '',
        },
        width: {
            type: Number,
            default: 580,
        },
        modalClass: {
            type: String,
            default: '',
        },
    },
    emits: ['update:open', 'confirm', 'cancel'],
    created() {
        // 验证至少有一个message或image
        if (!this.message && !this.image) {
            console.warn('ConfirmModal: 至少需要提供message或image属性之一')
        }
    },
    methods: {
        handleConfirm() {
            this.$emit('confirm')
            this.$emit('update:open', false)
        },
        handleCancel() {
            this.$emit('cancel')
            this.$emit('update:open', false)
        },
    },
}
</script>

<style scoped>
.confirm-content {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-size: 28px;
    color: #333333;
    text-align: center;
    padding: 30px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.image-container {
    margin-bottom: 20px;
}

.modal-image {
    width: 300px;
    height: 300px;
    object-fit: contain;
}

.message-container {
    padding: 0 20px;
}

.message-container.with-image {
    margin-top: 10px;
}

.confirm-footer {
    display: flex;
    justify-content: center;
    padding-bottom: 30px;
}

.confirm-btn {
    width: 200px;
    height: 72px;
    border-radius: 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cancel-btn {
    background: #ffffff;
    color: #3173c6;
    border: 1px solid #3173c6;
}

.confirm-btn-primary {
    background: #3173c6;
    color: #ffffff;
}

.cancel-btn:hover {
    background: #f7f7f7;
}

.cancel-btn:active {
    background: #f0f0f0;
}

.confirm-btn-primary:hover {
    background: #4285d8;
}

.confirm-btn-primary:active {
    background: #2864b7;
}
</style>

<style>
.confirm-dialog {
    z-index: 9999;
}
.confirm-dialog .ant-modal-content {
    padding: 0;
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.3s ease-in-out;
}
.confirm-dialog .ant-modal-title {
    height: 100px;
    background: #dbe6f1;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 36px;
    color: #03206a;
    display: flex;
    align-items: center;
    justify-content: center;
}
/* 自定义过渡动画 */
.fade-enter-active {
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}
.fade-leave-active {
    transition: all 0.2s cubic-bezier(0.4, 0, 1, 1);
}
.fade-enter-from,
.fade-leave-to {
    opacity: 0;
    transform: scale(0.9);
}
.fade-enter-to,
.fade-leave-from {
    opacity: 1;
    transform: scale(1);
}
</style> 