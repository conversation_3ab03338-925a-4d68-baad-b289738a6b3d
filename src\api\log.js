import request from '@/utils/dqRequest'
/**
 * 记录前台操作日志
 * @param data
 * {"personname":"","cardnumber":"","pagename":"","routerstatus":"","pageurl":"","clickdescription":"","logdate":""}
 * routerstatus：001进入，002保持，003离开
 * @returns {*}
 */
export function logStatic(data) {
    return request({
        module: 'base',
        url: '/log/static',
        method: 'post',
        platform: 'base',
        data: data,
    })
}
