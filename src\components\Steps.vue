<template>
    <div class="steps-component">
        <div v-for="(step,index) of steps" :key="step" class="step"
            :class="{'active': index == active,'completed': index < active}">
            <div class="circle">{{index+1}}</div>
            {{step}}
        </div>
    </div>
</template>

<script>
export default {
    name: 'Steps',
    props: {
        steps: {
            type: Array,
            default: () => [
                '阅读须知',
                '选择立案案由',
                '上传诉讼材料',
                '完善案件信息',
                '预览和提交',
                '提交完成',
            ],
        },
        active: {
            type: Number,
            default: 0,
        },
    },
}
</script>
<style scoped>
.steps-component {
    font-family: Source Han <PERSON>, Source Han Sans CN;
    font-weight: 400;
    font-size: 24px;
    color: #a1b1c5;
    display: flex;
    margin-left: 48px;
    position: relative; /* 新增 */
}

.step {
    margin: 0 56px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative; /* 新增 */
}

/* 新增连接线样式 */
.step:not(:first-child)::before {
    content: '';
    position: absolute;
    top: 17px;
    right: calc(50% + 17px + 15px); /* 从当前圆圈右侧开始 */
    width: calc(50% + 23px);
    height: 1px;
    border-top: 1px dashed #a1b1c5;
}

/* 新增连接线样式 */
.step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 17px; /* 圆圈半径 */
    left: calc(50% + 17px + 15px); /* 从当前圆圈右侧开始 */
    width: calc(50% + 23px); /* 总间距减去两个圆半径 */
    height: 1px;
    border-top: 1px dashed #a1b1c5;
}

.step.active,
.step.completed {
    color: #3173c6;
}

.step .circle {
    height: 34px;
    width: 34px;
    border-radius: 50%;
    border: 1px solid #a1b1c5;
    text-align: center;
    line-height: 30px; /* 修正为34px保持垂直居中 */
    margin-bottom: 4px;
    position: relative; /* 确保圆圈在连接线上方 */
    z-index: 1; /* 确保圆圈在连接线上方 */
    background: white; /* 防止连接线穿透 */
}

.step.active .circle,
.step.completed .circle {
    border: 1px solid #3173c6;
    background-color: #3173c6;
    color: #fff;
}

/* 激活状态的连接线 */
.step.completed:not(:first-child)::before,
.step.completed:not(:last-child)::after,
.step.active:not(:first-child)::before {
    border-top: 1px dashed #3173c6;
}
</style>