<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>文本分类演示</title>
  <style>
    body {
      font-family: 'Microsoft YaHei', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .container {
      border: 1px solid #eee;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    textarea {
      width: 100%;
      height: 150px;
      padding: 10px;
      margin-bottom: 15px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    button {
      background-color: #4285f4;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #3367d6;
    }
    .result {
      margin-top: 20px;
      padding: 15px;
      background-color: #f9f9f9;
      border-left: 4px solid #4285f4;
      display: none;
    }
    .result.show {
      display: block;
    }
    .example-buttons {
      margin-bottom: 15px;
    }
    .example-btn {
      margin-right: 10px;
      margin-bottom: 10px;
      background-color: #f1f1f1;
      color: #333;
    }
    .example-btn:hover {
      background-color: #e4e4e4;
    }
    h2 {
      color: #333;
      margin-bottom: 20px;
    }
    .confidence-bar {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
    }
    .confidence-bar .label {
      width: 250px;
      font-size: 14px;
    }
    .confidence-bar .bar {
      flex-grow: 1;
      background-color: #e0e0e0;
      height: 15px;
      border-radius: 3px;
      position: relative;
      margin-right: 10px;
    }
    .confidence-bar .fill {
      background-color: #4285f4;
      height: 100%;
      border-radius: 3px;
      position: absolute;
      top: 0;
      left: 0;
    }
    .confidence-bar .percentage {
      width: 50px;
      text-align: right;
      font-size: 14px;
    }
    .all-confidences {
      margin-top: 15px;
    }
    .highest {
      font-weight: bold;
    }
    .highest .fill {
      background-color: #34a853;
    }
  </style>
</head>
<body>
  <div class="container">
    <h2>文本分类系统演示</h2>
    
    <div class="example-buttons">
      <button class="example-btn" data-example="身份证">身份证示例</button>
      <button class="example-btn" data-example="起诉状">起诉状示例</button>
      <button class="example-btn" data-example="送达地址">送达地址示例</button>
      <button class="example-btn" data-example="委托书">委托书示例</button>
    </div>
    
    <textarea id="textInput" placeholder="请输入需要分类的文本内容..."></textarea>
    <button id="classifyBtn">进行文本分类</button>
    
    <div id="result" class="result">
      <h3>分类结果</h3>
      <p><strong>最可能的类别：</strong><span id="category"></span> (置信度: <span id="confidence"></span>)</p>
      
      <div class="all-confidences">
        <h4>所有类别置信度：</h4>
        <div id="confidenceBars"></div>
      </div>
    </div>
  </div>

  <!-- 先导入imgRec.js -->
  <script src="../utils/imgRec.js"></script>
  
  <script>
    // 示例文本
    const exampleTexts = {
      "身份证": "居民身份证 姓名：张三 性别：男 民族：汉 出生：1990年01月01日 住址：北京市海淀区",
      "起诉状": "起诉状 原告：李四，男，汉族 被告：王五，男，汉族 诉讼请求：1.判令被告支付货款10000元 事实与理由：原告与被告于2023年签订合同",
      "送达地址": "送达地址确认书 送达方式：邮寄 送达地址：北京市朝阳区 电话：13800138000 邮政编码：100000",
      "委托书": "授权委托书 委托人：张三 受托人：李四 委托事项：代为处理民事诉讼事宜 律师事务所：某某律师事务所 特别授权：可代为调解、和解"
    };

    document.addEventListener('DOMContentLoaded', function() {
      // 获取DOM元素
      const textInput = document.getElementById('textInput');
      const classifyBtn = document.getElementById('classifyBtn');
      const resultDiv = document.getElementById('result');
      const categorySpan = document.getElementById('category');
      const confidenceSpan = document.getElementById('confidence');
      const confidenceBarsDiv = document.getElementById('confidenceBars');
      
      // 点击分类按钮
      classifyBtn.addEventListener('click', function() {
        const text = textInput.value.trim();
        if (!text) {
          alert('请输入需要分类的文本内容');
          return;
        }
        
        // 调用imgRec函数进行分类
        try {
          const result = imgRec(text);
          
          // 显示最高置信度类别
          categorySpan.textContent = result.category;
          confidenceSpan.textContent = (result.confidence * 100).toFixed(2) + '%';
          
          // 显示所有类别的置信度
          confidenceBarsDiv.innerHTML = '';
          
          // 获取所有类别并按置信度降序排序
          const categories = Object.keys(result.allConfidences).sort((a, b) => {
            return result.allConfidences[b] - result.allConfidences[a];
          });
          
          // 为每个类别创建进度条
          categories.forEach(category => {
            const confidence = result.allConfidences[category];
            const percentage = (confidence * 100).toFixed(2) + '%';
            
            const barDiv = document.createElement('div');
            barDiv.className = 'confidence-bar';
            if (category === result.category) {
              barDiv.className += ' highest';
            }
            
            const labelDiv = document.createElement('div');
            labelDiv.className = 'label';
            labelDiv.textContent = category;
            barDiv.appendChild(labelDiv);
            
            const barContainer = document.createElement('div');
            barContainer.className = 'bar';
            
            const fillDiv = document.createElement('div');
            fillDiv.className = 'fill';
            fillDiv.style.width = percentage;
            barContainer.appendChild(fillDiv);
            
            barDiv.appendChild(barContainer);
            
            const percentDiv = document.createElement('div');
            percentDiv.className = 'percentage';
            percentDiv.textContent = percentage;
            barDiv.appendChild(percentDiv);
            
            confidenceBarsDiv.appendChild(barDiv);
          });
          
          resultDiv.classList.add('show');
        } catch (err) {
          alert('分类出错：' + err.message);
          console.error('分类错误:', err);
        }
      });
      
      // 示例按钮点击事件
      document.querySelectorAll('.example-btn').forEach(btn => {
        btn.addEventListener('click', function() {
          const exampleType = this.getAttribute('data-example');
          if (exampleTexts[exampleType]) {
            textInput.value = exampleTexts[exampleType];
          }
        });
      });
    });
  </script>
</body>
</html> 