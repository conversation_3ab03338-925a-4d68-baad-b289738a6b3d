const state = () => ({
  ajlxInfo: null, // 案件类型基本信息
  fyId: '2750', // 法院ID
  caseType: null, // 案件类型
  layyInfo: null, // 立案预约信息
  layyId: null, // 立案预约ID
  lafs: null, // 立案方式
  applyUserType: null, // 申请人类型
  isIp: null // 是否知识产权
})

const mutations = {
  SET_AJLX_INFO(state, info) {
    state.ajlxInfo = info
  },
  SET_FYID(state, info) {
    state.fyId = info
  },
  SET_CASE_TYPE(state, caseType) {
    state.caseType = caseType
  },
  SET_LAYY_INFO(state, info) {
    state.layyInfo = info
  },
  SET_LAYY_ID(state, id) {
    state.layyId = id
  },
  SET_LAFS(state, lafs) {
    state.lafs = lafs
  },
  SET_APPLY_USER_TYPE(state, type) {
    state.applyUserType = type
  },
  SET_IS_IP(state, isIp) {
    state.isIp = isIp
  }
}

const actions = {
  // 设置案件类型基本信息
  setAjlxInfo({ commit }, info) {
    commit('SET_AJLX_INFO', info)
  },
  // 设置法院ID
  setFYID({ commit }, info) {
    commit('SET_FYID', info)
  },
  // 设置案件类型
  setCaseType({ commit }, caseType) {
    commit('SET_CASE_TYPE', caseType)
  },
  // 设置立案预约信息
  setLayyInfo({ commit }, info) {
    commit('SET_LAYY_INFO', info)
  },
  // 设置立案预约ID
  setLayyId({ commit }, id) {
    commit('SET_LAYY_ID', id)
  },
  // 设置立案方式
  setLafs({ commit }, lafs) {
    commit('SET_LAFS', lafs)
  },
  // 设置申请人类型
  setApplyUserType({ commit }, type) {
    commit('SET_APPLY_USER_TYPE', type)
  },
  // 设置是否知识产权
  setIsIp({ commit }, isIp) {
    commit('SET_IS_IP', isIp)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
} 