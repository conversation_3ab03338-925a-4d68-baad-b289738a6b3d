import request from '@/utils/dqRequest'
import { DQUpdate } from 'dq-eqc'
import messageConfirm from '@/plugins/messageConfirm'
/**
 * 记录前台操作日志
 * @param data
 * @returns {*}
 */
function getScriptVersionList() {
    return request({
        module: 'suit',
        url: '/project/getScriptVersionList',
        method: 'get',
        platform: 'sls',
    })
}


export async function checkUpdate() {

    let latestVersion = await getScriptVersionList()
    let needUpdateList = []
    for (let item of latestVersion) {
        needUpdateList.push({
            type: item.scriptType,
            url: item.scriptUrl,
            name: item.scriptName,
            ver: item.versionMain + '.' + item.versionSub
        })
    }
    let res = await DQUpdate.check(needUpdateList)
    if (res && res.code == 200) {
        await messageConfirm({
            message: '检查到存在新版本，是否立即更新',
            confirmText: '确定',
        })
        return await DQUpdate.update(needUpdateList)
    }

}