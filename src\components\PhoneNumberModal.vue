<template>
    <div>
        <a-modal :open="open" title="手机号码" :footer="null" :closable="false" width="1087px" :centered="true"
            wrapClassName="phone-number-dialog" :maskClosable="false">
            <div class="modal_content">
                <div class="phone-form">
                    <FormItem label="手机号码" :required="true" :span="2">
                        <input v-model="form.sjhm" placeholder="请输入手机号码" />
                    </FormItem>
                </div>
            </div>
            <div class="modal_footer">
                <div class="modal_btn normal" @click="$emit('update:open', false)">
                    取消
                </div>
                <div class="modal_btn" @click="submit">
                    提交
                </div>
            </div>
        </a-modal>
    </div>
</template>

<script>
import FormItem from '@/components/FormItem.vue'
export default {
    components: {
        FormItem,
    },
    props: {
        open: {
            type: Boolean,
            required: true,
        },
    },
    data() {
        return {
            form: {
                bh: '',
                sjhm: '',
            },
        }
    },
    methods: {
        submit() {
            // 验证手机号码格式
            const phoneRegex = /^\d{11}$/
            if (!this.form.sjhm || !phoneRegex.test(this.form.sjhm)) {
                this.$message.error('请输入11位手机号码')
                return
            }

            this.$rpa
                .dealFetch(
                    'https://zxfw.court.gov.cn/yzw/yzw-zxfw-yhfw/api/v1/grxx/dxsd/add',
                    {
                        method: 'POST',
                        body: this.form,
                    }
                )
                .then((res) => {
                    if (res.code == 200) {
                        this.$message.success('提交成功')
                        this.$emit('updateList')
                        this.$emit('update:open', false)
                    }
                })
        },
    },
}
</script>


<style scoped>
.phone-form {
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
}
</style>

<style>
.phone-number-dialog .ant-modal-content {
    padding: 0;
    border-radius: 20px;
    overflow: hidden;
}

.phone-number-dialog .ant-modal-title {
    height: 100px;
    background: #dbe6f1;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 42px;
    color: #03206a;
    display: flex;
    align-items: center;
    justify-content: center;
}

.phone-number-dialog .modal_content {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 24px;
    color: #333333;
    padding: 20px 40px;
    border-bottom: 2px solid #dbe6f1;
    height: 670px;
}

.phone-number-dialog .modal_footer {
    height: 112px;
    padding: 0 50px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.phone-number-dialog .modal_btn {
    width: 216px;
    height: 72px;
    background: #3173c6;
    border-radius: 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20px;
    cursor: pointer;
    border: none;
}
.phone-number-dialog .modal_btn.normal {
    background: #fff;
    color: #3173c6;
    border: 1px solid #3173c6;
}
</style>

