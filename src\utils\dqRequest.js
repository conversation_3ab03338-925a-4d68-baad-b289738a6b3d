import axios from 'axios'
import CryptoJS from 'crypto-js/crypto-js'
import store from '../store'

// 创建axios实例
const service = axios.create({
    timeout: 15000 // 请求超时时间
})

// 密钥
var SECRET = 'psaddbpi0spjk0c2mea7r88u3th4qrt8'
// request拦截器
service.interceptors.request.use(config => {
    // 添加超时时长
    if (config.timeout !== undefined) {
        service.defaults.timeout = config.timeout
    } else {
        service.defaults.timeout = 15000
    }
    // 获取URL
    let baseUrl = 'http://172.28.184.68:59781';
    let platformPort;
    if (config.platform === 'sls') {
        // baseUrl = store.getters.baseSlsUrl;
        platformPort = '/151';
    } else if (config.platform === 'user') {
        // baseUrl = store.getters.baseUserUrl;
        platformPort = '/86';
    } else if (config.platform === 'base') {
        // baseUrl = store.getters.baseBaseUrl;
        platformPort = '/81';
    } else if (config.platform === 'center') {
        // baseUrl = process.env.CENTER_URL;
        platformPort = '';
    }
    config.url = baseUrl + authFormat(config, platformPort);
    return config
}, error => {
    // Do something with request error
    console.log(error) // for debug
    Promise.reject(error)
})

// respone拦截器
service.interceptors.response.use(
    response => {
        const res = response.data
        return res
    },
    error => {
        return Promise.reject(error)
    }
)

var authFormat = (config, platformPort) => {
    let type = 'admin';
    let timestamp = new Date().getTime();
    let token = sessionStorage.getItem('token');
    let key = process.env.KEY;
    let secretStr = SECRET;
    let suitId = sessionStorage.getItem("h5SuitId");


    if (config.type) {
        type = config.type;
    }

    // 如果接口类型是app 更改为app key secret
    if (type === 'app') {
        key = process.env.APP_KEY;
        secretStr = process.env.APP_SCREAT;
    }
    let url
    if (platformPort) {
        url = platformPort + "/" + type + "/" + config.module + config.url;
    } else {
        url = "/" + type + "/" + config.module + config.url;
    }
    url += url.indexOf("?") === -1 ? "?" : "&";
    url += "token=" + token;
    url += "&timestamp=" + timestamp;
    url += suitId ? ("&suitId=" + suitId) : "";
    url += "&key=" + (key ? key : "");
    var dateStr = formatDateToYYYYMMDD()
    var au = CryptoJS.MD5(dateStr + 'dqist').toString();
    url += "&au=" + au

    let requestURI = url.split("?")[0];
    requestURI = url.startsWith("/") ? requestURI : "/" + requestURI;
    requestURI = url.endsWith("/") ? requestURI.substring(0, requestURI.length - 1) : requestURI;
    // 请求参数
    let args = [];
    let query = url.split("?")[1];
    let pairs = query.split("&");
    for (let i = 0; i < pairs.length; i++) {
        let pos = pairs[i].indexOf('=');
        if (pos === -1) continue;
        let argname = pairs[i].substring(0, pos);
        let value = pairs[i].substring(pos + 1);
        value = decodeURIComponent(value);
        let arg = {};
        arg.key = argname;
        arg.value = value;
        args.push(arg);
    }
    args = args.sort(function (a, b) {
        return a.key > b.key ? 1 : -1;
    });
    let paramstring = "";
    for (let index = 0; index < args.length; index++) {
        paramstring += args[index].key + args[index].value;
    }
    // 非get方法的body添加
    var body = "";
    if ("get" !== config.method.toLowerCase() && config.data) {
        // 只有非sls才转字符
        body = JSON.stringify(config.data);
        if (config.platform !== 'sls') {
            // 防止xss注入
            body = body.replace(new RegExp(">", "gm"), '＞');
            body = body.replace(new RegExp("<", "gm"), '＜');
            body = body.replace(new RegExp("\'", "gm"), '‘');
            body = body.replace(new RegExp("\\$", "gm"), '＄');
        }
        config.data = JSON.parse(body);


    }
    var sign = CryptoJS.HmacMD5(requestURI + paramstring + body, secretStr);
    url = url + "&sign=" + sign;

    return url;
};

// 最终推荐版本
function formatDateToYYYYMMDD(date = new Date()) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
}

export default service
