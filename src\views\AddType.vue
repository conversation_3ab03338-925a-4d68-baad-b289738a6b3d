<template>
    <div class="action-con">
        <Container>
            <div class="con">
                请选择立案方式
                <div class="border">
                    <img src="@/assets/addType/add1.png" @click="goUserType('1')" />
                    <img src="@/assets/addType/add2.png" @click="goUserType('2')"/>
                </div>
            </div>
        </Container>
    </div>
</template>

<script>
import Container from '@/components/Container.vue'
export default {
    name: 'CaseType',
    components: {
        Container,
    },
    methods: {
        goUserType(lafs) {
            // 将立案方式存入store
            this.$store.dispatch('layy/setLafs', lafs)
            // 跳转到下一页
            this.$router.push('/userType')
        }
    }
}
</script>

<style scoped>
.con {
    display: flex;
    align-items: center;
    height: 100%;
    flex-direction: column;
    padding: 20px;
    font-family: Source <PERSON> Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 30px;
    color: #333333;
    font-weight: bold;
}
.con img {
    margin: 50px;
}

.border {
    width: 100%;
    height: 564px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #a1b1c5;
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>