<template>
    <div>
        <Container>
            <template #head>
                <div class="ip_head">民事一审
                    <Steps :active="3" />
                </div>

            </template>
            <div class="con">
                {{ $store.getters.lafsName }}-{{ $store.getters.applyCaseTypeName}}
                <div style="display: flex" v-if="!pdfImg">
                    <div class="border left_con">
                        <div v-for="(tab,index) of tabs" class="tab"
                            :class="{active: activeTab == index,require: true,undone: !isDoneMap[index]}"
                            @click="activeTab = index" :key="tab">{{tab}}</div>
                    </div>
                    <div class="right_con" v-if="activeTab == 0">
                        <div class="ssqq_con" ref="ssqq">
                            <div class="ssqq_item" v-for="(item,index) of ssqqlist" :key="index">
                                <div class="ssqq_item_title center">诉讼请求{{index+1}}</div>
                                <div class="ssqq_item_content">
                                    <textarea type="textarea" placeholder="请输入诉讼请求" v-model="ssqqlist[index]" />
                                    <img src="@/assets/del_circle.png" style="height: 40px"
                                        @click="ssqqlist.splice(index,1)" />
                                </div>
                            </div>
                            <div class="add_ssqq center" @click="ssqqlist.push('')">
                                +添加诉讼请求
                            </div>
                        </div>

                        <div class="page_bar">
                            <img src='@/assets/pageUp.png' @click="scroll('ssqq',-1)" />
                            <img src='@/assets/pageDown.png' @click="scroll('ssqq',1)" />
                        </div>
                    </div>
                    <div class="right_con" v-if="activeTab == 1">
                        <div class="ssqq_con">
                            <div class="ssqq_item">
                                <div class="ssqq_item_title center" style="height: 560px">事实与理由</div>
                                <div class="ssqq_item_content">
                                    <textarea type="textarea" placeholder="请输入事实与理由" v-model="ssly" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="pdf_con" v-else>
                    <div class="pdf_scroll" ref="pdfScroll">
                        <img :src="pdfImg" />
                    </div>
                    <div class="pdf_page_bar">
                        <img src='@/assets/pageUp.png' @click="scroll('pdfScroll',-1)" />
                        <img src='@/assets/pageDown.png' @click="scroll('pdfScroll',1)" />
                    </div>
                </div>
                <div class="btns">
                    <div class="ip_btn" @click="goBack">上一步</div>
                    <div class="ip_btn deep_btn" @click="goNext" v-if="isDone">下一步</div>
                </div>
            </div>
        </Container>
        <SignModal v-model:open="signModalOpen" @confirm="confirmSign" @cancel="cancelSign" />
    </div>
</template>

<script>
import Container from '@/components/Container.vue'
import Steps from '@/components/Steps.vue'
import SignModal from '@/components/SignModal.vue'
import * as pdfjsLib from 'pdfjs-dist'
export default {
    components: {
        Container,
        Steps,
        SignModal,
    },
    data() {
        return {
            activeTab: 0,
            tabs: ['诉讼请求', '事实与理由'],
            ssqqlist: [''],
            ssly: '',
            signModalOpen: false,
            pdfImg: null,
            layyid: this.$store.getters.layyid,
        }
    },
    computed: {
        isDoneMap() {
            let isDoneMap = {
                0: !this.ssqqlist.some((item) => !item),
                1: !!this.ssly,
            }
            return isDoneMap
        },
        isDone() {
            return Object.values(this.isDoneMap).every((item) => item)
        },
    },
    async mounted() {
        pdfjsLib.GlobalWorkerOptions.workerSrc = new URL(
            `../../utils/pdf.worker.min.js`,
            import.meta.url
        ).toString()
        let res = await this.$rpa.dealFetch(
            `https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/layyxq/${this.layyid}/0`,
            {
                method: 'GET',
            }
        )
        let ssqqlist = res.data.layy.ssqq
        if (localStorage.getItem(`ssqqlist_${this.layyid}`)) {
            ssqqlist = localStorage.getItem(`ssqqlist_${this.layyid}`)
        }
        this.ssqqlist = ssqqlist.split('；').filter((item) => item)

        let ssly = res.data.layy.ssly
        if (localStorage.getItem(`ssly_${this.layyid}`)) {
            ssly = localStorage.getItem(`ssly_${this.layyid}`)
        }
        this.ssly = ssly
    },
    methods: {
        goBack() {
            localStorage.setItem(
                `ssqqlist_${this.layyid}`,
                this.ssqqlist.join('；') + '；'
            )
            localStorage.setItem(`ssly_${this.layyid}`, this.ssly)
            this.$router.go(-1)
        },
        async goNext() {
            if (!this.isDone) {
                return
            }
            let param = {
                layyid: this.layyid,
                nsqlaay: null,
                ssqq: this.ssqqlist.join('；') + '；',
                ssly: this.ssly,
            }
            await this.$rpa.dealFetch(
                'https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/ssqq',
                {
                    method: 'POST',
                    body: param,
                }
            )
            // 如果不是起诉状立案、且没有生成，则给他生成一份起诉状
            if (!this.pdfUrl && this.$store.getters.lafs == '1') {
                await this.$messageConfirm({
                    message:
                        '系统即将为您制作起诉状，请您先选择预留签名，如果您没有预留签名请您先扫码签名，为他人申请立案请您选择当事人的签名',
                    confirmText: '确定',
                })
                this.signModalOpen = true
                return
            }
            localStorage.removeItem(`ssly_${this.layyid}`)
            localStorage.removeItem(`ssqqlist_${this.layyid}`)
            this.$router.push('/Step5')
            return
        },

        async confirmSign(sign) {
            let path = sign.osspath
            let res = await this.$rpa.dealFetch(
                'https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/ssclfj/actions/zzqsz',
                {
                    method: 'POST',
                    body: {
                        layyid: this.$store.getters.layyid,
                        qmPath: path,
                    },
                }
            )
            res = JSON.parse(res.data)
            this.pdfUrl = res.url
            // 将此pdfPath网络地址的pdf转化成图片
            this.pdfImg = await this.convertPdfUrlToBase64Image(this.pdfUrl)
            this.signModalOpen = false
        },

        async convertPdfUrlToBase64Image(url) {
            // const loadingTask = pdfjsLib.getDocument(url)
            // const pdf = await loadingTask.promise
            // const numPages = pdf.numPages
            // const page = await pdf.getPage(1)
            // const viewport = page.getViewport({ scale: 2.0 })

            // const canvas = document.createElement('canvas')
            // const context = canvas.getContext('2d')
            // canvas.height = viewport.height
            // canvas.width = viewport.width

            // const renderContext = {
            //     canvasContext: context,
            //     viewport: viewport,
            // }

            // await page.render(renderContext).promise

            // return canvas.toDataURL('image/png')
            // 1. 加载PDF文档
            const loadingTask = pdfjsLib.getDocument(url)
            const pdf = await loadingTask.promise
            const totalPages = pdf.numPages

            // 2. 创建离屏Canvas渲染所有页面
            const pagePromises = []
            for (let i = 1; i <= totalPages; i++) {
                pagePromises.push(
                    pdf.getPage(i).then((page) => this.renderPage(page, 2))
                )
            }

            // 3. 并行渲染所有页面
            const pages = await Promise.all(pagePromises)

            // 4. 计算总高度并创建最终Canvas
            const totalHeight = pages.reduce(
                (sum, canvas) => sum + canvas.height,
                0
            )
            const maxWidth = Math.max(...pages.map((c) => c.width))

            const finalCanvas = document.createElement('canvas')
            finalCanvas.width = maxWidth
            finalCanvas.height = totalHeight
            const ctx = finalCanvas.getContext('2d')

            // 5. 拼接所有页面
            let yPos = 0
            pages.forEach((canvas) => {
                ctx.drawImage(canvas, 0, yPos)
                yPos += canvas.height
            })

            // 6. 导出为图片
            return finalCanvas.toDataURL('image/png')
        },
        // 单页渲染函数
        async renderPage(page, scale) {
            const viewport = page.getViewport({ scale })
            const canvas = document.createElement('canvas')
            canvas.width = viewport.width
            canvas.height = viewport.height

            await page.render({
                canvasContext: canvas.getContext('2d'),
                viewport,
            }).promise

            return canvas
        },
        scroll(ref, count) {
            let dom = this.$refs[ref]
            dom.scrollBy({
                top: dom.offsetHeight * 0.5 * count,
                behavior: 'smooth',
            })
        },
    },
}
</script>

<style scoped>
.left_con {
    width: 218px;
    margin-right: 40px;
    align-items: center;
}

.left_con .tab {
    width: 100%;
    height: 50%;
    background: #dbe6f1;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #3173c6;
    display: flex;
    justify-content: center;
    align-items: center;
}

.left_con > .tab:not(:last-child) {
    border-bottom: 1px solid #a1b1c5;
}

.tab.require::before {
    content: '*';
    margin-right: 4px;
}

.tab.disabled {
    color: #a1b1c5;
}

.tab.undone {
    color: #c63131;
}

.tab.active {
    background: #3173c6;
    color: #fff;
}
.tab.undone.active {
    color: #f9a4a4;
}
.right_con {
    margin-top: 20px;
    width: 1482px;
    height: 564px;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    position: relative;
    border-radius: 10px;
    border: 1px solid #a1b1c5;
    overflow: hidden;
}
.ssqq_con {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: auto;
}

.ssqq_item {
    width: 100%;
    border-right: 1px solid #a1b1c5;
    border-bottom: 1px solid #a1b1c5;
    display: flex;
}

.ssqq_item_title {
    background: #f1f5ff;
    border-right: 1px solid #a1b1c5;
    height: 143px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 24px;
    color: #333333;
    width: 218px;
}

.ssqq_item_content {
    width: 100%;
    padding: 12px;
    display: flex;
    align-items: center;
}

.ssqq_item_content textarea {
    width: 100%;
    height: 100%;
    border: none;
    outline: none;
    font-size: 24px;
}

.add_ssqq {
    width: 270px;
    height: 60px;
    background: #3173c6;
    border-radius: 10px 10px 10px 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 24px;
    color: #ffffff;
    margin: 20px;
    flex: none;
}
.page_bar {
    width: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
}
.pdf_page_bar {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    height: 170px;
    position: absolute;
    right: -60px;
    top: 197px;
}
.pdf_con {
    width: 1354px;
    height: 564px;
    background: #dbe6f1;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #a1b1c5;
    padding: 20px;
    margin-top: 20px;
    position: relative;
}

.pdf_scroll {
    width: 100%;
    height: 100%;
    overflow: auto;
}

.pdf_scroll::-webkit-scrollbar {
    display: none;
}

.pdf_scroll img {
    width: 100%;
}
</style>

<style scoped>
.con {
    display: flex;
    align-items: center;
    height: 100%;
    flex-direction: column;
    padding: 20px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 30px;
    color: #333333;
    font-weight: bold;
}

.border {
    height: 226px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #a1b1c5;
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.ip_head {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 32px;
    color: #333333;
}

.btns {
    display: flex;
    justify-content: center;
    width: 100%;
    position: relative;
}

.ip_btn {
    width: 216px;
    height: 72px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #3173c6;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #3173c6;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20px 12px 0 12px;
}

.deep_btn {
    background: #3173c6;
    color: #ffffff;
}
</style>