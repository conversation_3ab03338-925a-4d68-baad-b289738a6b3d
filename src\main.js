import { createApp } from 'vue'
import Antd from 'ant-design-vue';
import { message } from 'ant-design-vue';
import App from './App.vue'
import router from './router'
import store from './store'
import rpa from './utils/rpa'
import messageConfirm from './plugins/messageConfirm'
import { createWebsocketInstance } from 'dq-eqc'
import * as request from './utils/request'
import './styles/font.css'
import './styles/main.css'
import 'ant-design-vue/dist/reset.css';

// 创建websocket插件
const websocketPlugin = {
    install: (app) => {
        // 初始化为null
        app.config.globalProperties.$websocketInstance = null

        // 提供设置websocket实例的方法
        app.config.globalProperties.$setWebsocketInstance = (instance) => {
            app.config.globalProperties.$websocketInstance = instance
        }
    }
}

const app = createApp(App).use(store).use(router)
app.config.globalProperties.$rpa = rpa
app.config.globalProperties.$message = message
app.config.globalProperties.$http = request // 注册HTTP请求工具

function getText(list, value) {
    let item = list.find((t) => t.value == value)
    return item ? item.text : '请选择'
}
app.config.globalProperties.$getText = getText
// window.addEventListener('unhandledrejection', (event) => {
//     console.error('[未处理的Promise错误]', event.reason)
//     // 阻止浏览器默认的错误打印
//     event.preventDefault()
//     event.stopImmediatePropagation() // 阻止其他监听器处理
// })
function needScroll(dom) {
    return dom.scrollHeight > dom.clientHeight
}
app.config.globalProperties.$needScroll = needScroll
// 使用websocket插件
app.use(websocketPlugin)
app.use(Antd)
app.use(messageConfirm)
app.mount('#app')