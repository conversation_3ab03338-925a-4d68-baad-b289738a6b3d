<template>
    <a-modal :open="open" title="确认签名" :footer="null" :closable="false" width="1340px" :centered="true"
        :destroyOnClose="true" wrapClassName="signDialog">
        <div class="modal_content" ref="signList">
            <div class="modal_item">
                <img :src="QRCode" class="qr" />扫码新增
            </div>
            <div class="modal_item sign_item" :class="{active: activeSign && sign.bh == activeSign.bh}"
                v-for="sign of signList" :key="sign.bh" @click="chooseSign(sign)">
                <div class="modal_img center"><img :src="sign.url" /></div>
                <div class="modal_text">
                    <div>版本号</div>
                    <div>{{sign.version}}</div>
                </div>
                <div class="modal_text">
                    <div>签署时间</div>
                    <div>{{sign.qssj}}</div>
                </div>
            </div>
            <div class="modal_item sign_item" style="height: 0px;border: none">
            </div>
            <div class="scrollBar" v-if="needScroll">
                <img src="@/assets/pageUp.png" @click="scroll(-1)" />
                <img src="@/assets/pageDown.png" @click="scroll(1)" />
            </div>
        </div>
        <div class="modal_footer">
            <div class="modal_btn normal" @click="handleCancel">
                关闭
            </div>
            <div class="modal_btn" @click="confirm" v-if="activeSign">
                确定
            </div>
        </div>
    </a-modal>
</template>

<script>
import QRCode from 'qrcode'
export default {
    name: 'SignModal',
    props: {
        open: {
            type: Boolean,
            required: true,
        },
    },
    components: {},
    emits: ['confirm', 'cancel'],
    data() {
        return {
            signList: [],
            needScroll: false,
            QRCode: '',
            timeout: null,

            activeSign: null,
        }
    },
    watch: {
        open(val) {
            if (this.timeout) {
                clearTimeout(this.timeout)
            }
            if (val) {
                this.getSignList()
                this.generateQRCode()
            } else {
                this.activeSign = null
            }
        },
    },
    mounted() {},
    beforeUnmount() {
        if (this.timeout) {
            clearTimeout(this.timeout)
        }
    },
    methods: {
        async generateQRCode() {
            let res = await this.$rpa.dealFetch(
                'https://zxfw.court.gov.cn/yzw/yzw-zxfw-yhfw/api/v1/sjscwj/saveUuid',
                {
                    method: 'GET',
                }
            )
            this.uuid = res.data.id
            this.checkQrStatus(this.uuid)

            try {
                // 方式2：生成Data URL（图片格式）
                this.QRCode = await QRCode.toDataURL(
                    'https://zxfw.court.gov.cn/zxfw/index.html#/pagesGrxx/app/signature/index?isClose=false&id=' +
                        this.uuid,
                    {
                        width: 200,
                        margin: 2,
                        color: {
                            dark: '#000000', // 二维码颜色
                            light: '#ffffff', // 背景色
                        },
                    }
                )
            } catch (err) {
                console.error('生成二维码失败:', err)
            }
        },
        async getSignList() {
            this.signList = (
                await this.$rpa.dealFetch(
                    'https://zxfw.court.gov.cn/yzw/yzw-zxfw-ajfw/api/v1/dzqm/dzqmList',
                    {
                        method: 'POST',
                        body: { pageNum: 1, pageSize: 100 },
                    }
                )
            ).data.data
            await this.$nextTick()
            this.needScroll = this.$needScroll(this.$refs.signList)
        },
        scroll(count) {
            this.$refs.signList.scrollBy({
                top: count * 300,
                behavior: 'smooth',
            })
        },
        checkQrStatus(uuid) {
            this.$rpa
                .dealFetch(
                    `https://zxfw.court.gov.cn/yzw/yzw-zxfw-yhfw/api/v1/sjscwj/state?id=${uuid}`,
                    {
                        method: 'GET',
                    },
                    false
                )
                .then((res) => {
                    let czmsg = res.data.czmsg
                    if (czmsg == '3') {
                        this.getSignList()
                        this.generateQRCode()
                    } else {
                        this.timeout = setTimeout(() => {
                            this.checkQrStatus(uuid)
                        }, 1000)
                    }
                })
        },
        chooseSign(sign) {
            this.activeSign = sign
        },
        handleCancel() {
            this.$emit('cancel')
        },
        confirm() {
            this.$emit('confirm', this.activeSign)
        },
    },
}
</script>

<style scoped>
.modal_item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    width: 369px;
    height: 350px;
    background: #f1f5ff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #a1b1c5;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #333333;
    overflow: hidden;
}

.modal_item.active {
    border: 1px solid #3173c6;
    color: #0b7cff;
}

.sign_item {
    justify-content: unset;
}

.modal_item .qr {
    width: 200px;
    height: 200px;
    background: #9f9f9f;
    margin-bottom: 10px;
}

.modal_item .modal_img {
    height: 242px;
    width: 100%;
    background: #fff;
}

.modal_img > img {
    height: 100%;
}

.modal_text {
    height: 24px;
    line-height: 24px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 24px;
    color: #78808a;
    display: flex;
    margin-top: 20px;
}

.modal_text > div:first-of-type {
    width: 96px;
    text-align: right;
}

.modal_text > div:last-of-type {
    width: 232px;
    text-align: left;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 24px;
    color: #333333;
    margin-left: 10px;
}

.scrollBar {
    position: absolute;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 170px;
    top: 285px;
    right: 20px;
}

.scrollBar img {
    width: 40px;
    height: 40px;
}
</style> 
<!-- 编辑页面样式 -->
<style>
.signDialog .ant-modal-body {
    position: relative;
}
.signDialog .ant-modal-content {
    padding: 0;
    border-radius: 20px;
    overflow: hidden;
}
.signDialog .ant-modal-header {
    margin-bottom: 0px;
}
.signDialog .ant-modal-title {
    height: 100px;
    background: #dbe6f1;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 42px;
    color: #03206a;
    display: flex;
    align-items: center;
    justify-content: center;
}

.signDialog .modal_content {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 30px;
    color: #333333;
    padding: 20px 80px 10px 50px;
    border-bottom: 2px solid #dbe6f1;
    height: 738px;
    overflow: auto;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.signDialog .modal_footer {
    height: 112px;
    padding: 0 50px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.signDialog .modal_btn {
    width: 216px;
    height: 72px;
    background: #3173c6;
    border: 1px solid #3173c6;
    border-radius: 10px 10px 10px 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20px;
}

.signDialog .modal_btn.normal {
    background: #fff;
    color: #3173c6;
}
</style>