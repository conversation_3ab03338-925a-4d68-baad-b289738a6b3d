import { createVNode, render } from 'vue'
import ConfirmModal from '@/components/ConfirmModal.vue'

let messageInstance = null
let currentValue = false
const main = (options) => {
  return new Promise((resolve, reject) => {
    // 如果已存在实例，先移除
    if (messageInstance) {
      // 如果多次消息提示一致，不再提示
      if (messageInstance.config.message == options.message) {
        return
      }
      try {
        // 检查元素是否仍然是document.body的子元素
        if (document.body.contains(messageInstance.el)) {
          document.body.removeChild(messageInstance.el)
        }
      } catch (error) {
        console.warn('移除旧的消息确认框失败:', error)
      }
      messageInstance = null
    }

    // 创建一个div作为容器
    const container = document.createElement('div')

    // 使用options或默认配置
    const config = {
      message: options.message || '',
      title: options.title || '提示',
      cancelText: options.cancelText || '取消',
      confirmText: options.confirmText || '确定',
      showCancel: options.showCancel || false,
      image: options.image || '',
      width: options.width || 580,
      modalClass: options.modalClass || '',
    }

    // 验证至少有一个message或image
    if (!config.message && !config.image) {
      console.error('$messageConfirm: 必须提供message或image属性之一')
      reject(new Error('必须提供message或image属性之一'))
      return
    }

    // 内部状态
    currentValue = true
    // 创建虚拟节点
    const vnode = createVNode(ConfirmModal, {
      ...config,
      open: currentValue,
      'onUpdate:open': (value) => {
        currentValue = value
        if (!value) {
          // 关闭弹窗后延迟移除DOM，保证过渡效果
          setTimeout(() => {
            render(null, container)
            try {
              // 检查元素是否仍然是document.body的子元素
              if (document.body.contains(container)) {
                document.body.removeChild(container)
              }
            } catch (error) {
              console.warn('移除消息确认框容器失败:', error)
            }
            messageInstance = null

          }, 250)
        }
      },
      onConfirm: () => {
        resolve(true)
      },
      onCancel: () => {
        reject(new Error('User cancelled'))
      }
    })

    // 渲染到容器
    render(vnode, container)
    document.body.appendChild(container)

    // 保存实例引用
    messageInstance = {
      el: container,
      vnode,
      config
    }
  })
}
export const messageConfirm = main
export default {
  install(app) {
    app.config.globalProperties.$messageConfirm = main
  }
}