<template>
    <div class="action-con">
        <Container>
            <template #head>
                <div class="user_head">民事一审
                </div>
            </template>
            <div class="con">
                {{ $store.getters.lafsName }}
                <div class="border">
                    <img src="@/assets/userType/benren.png" @click="goNext(applyUserType.BENREN)" />
                    <img src="@/assets/userType/taren.png"  @click="goNext(applyUserType.TAREN)" />
                </div>
            </div>
        </Container>
    </div>
</template>
<script>
import Container from '@/components/Container.vue'
import { APPLY_USER_TYPE } from '@/utils/bizConstant'
export default {
    name: 'UserType',
    components: {
        Container,
    },
    data() {
        return {
            applyUserType: APPLY_USER_TYPE,
        }
    },
    methods: {
        goNext(type) {
            // 保存到localStorage
            localStorage.setItem('userType', type)
            
            // 保存到store
            this.$store.dispatch('layy/setApplyUserType', type)
            
            // 跳转到下一页
            this.$router.push('/ipType')
        },
    },
}
</script>

<style scoped>
.con {
    display: flex;
    align-items: center;
    height: 100%;
    flex-direction: column;
    padding: 20px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 30px;
    color: #333333;
    font-weight: bold;
}
.con img {
    margin: 50px;
}

.border {
    width: 100%;
    height: 564px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #a1b1c5;
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user_head {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 32px;
    color: #333333;
}
</style>