<template>
    <a-modal :open="open" title="收款账号确认书" :footer="null" :closable="false" width="1088px" :centered="true"
        wrapClassName="addressDialog">
        <template v-if="!formData">
            <div class="modal_content">

                <div class="modal_item" v-for="(skzh,index) of skzhlist" :key="skzh" @click="chooseSkzh(index)"
                    :class="{active: activeIndex == index}">
                    <img :src="activeIndex == index ?radioChecked:radio" />
                    <div class="item_con">
                        <div class="item_head">{{skzh.skrxm}}
                            <div><img src="@/assets/edit.png" @click.stop="edit(skzh)" /><img src="@/assets/del.png"
                                    @click.stop="del(skzh.bh)" /></div>
                        </div>
                        <div class="item_content">
                            <div class="item_content_left">
                                <div class="content_title">联系方式</div>
                                <div class="content_text">{{skzh.lxfs}}</div>
                            </div>
                            <div class="item_content_right">
                                <div class="content_title">开户行</div>
                                <div class="content_text">{{skzh.skrkhh}}</div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <div class="modal_footer">
                <div class="add_btn center" @click="handleAdd">+添加</div>

                <div class="modal_btn normal" @click="handleCancel">
                    关闭
                </div>
                <div class="modal_btn" @click="confirm" v-if="activeIndex != null">
                    确定
                </div>
            </div>
        </template>
        <template v-else>
            <div class="modal_content">
                <div class="tabs">
                    <div v-for="sfbrzh of sfbrzhCodes" :key="sfbrzh.value" class="tab center"
                        :class="{active: formData.sfbrzh == sfbrzh.value}" @click="chooseSfbr(sfbrzh)">
                        {{sfbrzh.text}}</div>

                </div>
                <div class="address_form">
                    <FormItem label="收款人姓名" :required="true">
                        <input v-model="formData.skrxm" placeholder="请输入" />
                    </FormItem>
                    <FormItem :label="formData.sfbrzh == 3 ? '统一社会信用代码' : '身份证'" :required="true">
                        <input v-model="formData.zjhm" placeholder="请输入" />
                    </FormItem>
                    <FormItem label="开户行类别" :required="true">
                        <CustomSelect v-model="formData.khhlb" :list="khhlist">
                            <div class="selectDiv center">
                                {{$getText(khhlist,formData.khhlb)}}
                            </div>
                        </CustomSelect>
                    </FormItem>
                    <FormItem label="收款人开户行" :required="true">
                        <CustomSelect v-model="formData.lhh" filterable remote width="600"
                            :remoteMethod="(searchText) => getKhhList(formData.khhlb,searchText,formData.lhh)"
                            @change="updateSkrkhh">
                            <div class="selectDiv center">
                                {{formData.skrkhh}}
                            </div>
                        </CustomSelect>
                    </FormItem>
                    <FormItem label="联行号" :required="true">
                        <input v-model="formData.lhh" placeholder="请输入" disabled />
                    </FormItem>
                    <FormItem label="收款账户" :required="true">
                        <input v-model="formData.skrzh" placeholder="请输入" />
                    </FormItem>
                    <FormItem label="联系方式" :required="true">
                        <input v-model="formData.lxfs" placeholder="请输入" />
                    </FormItem>
                </div>
                <div class="gznr">
                    <div class="center">告知内容</div>
                    <img src="@/assets/pageUp.png" @touchstart="scroll(-1)" @touchend="scrollEnd" />
                    <img src="@/assets/pageDown.png" @touchstart="scroll(1)" @touchend="scrollEnd" />
                    <div ref="gznr">{{gznr}}</div>

                </div>
            </div>
            <div class="modal_footer">
                <div class="modal_btn normal" @click="toList">
                    关闭
                </div>
                <div class="modal_btn" @click="toSign">
                    确定
                </div>
            </div>
        </template>
        <SignModal v-model:open="signModalOpen" @cancel="signModalOpen = false" @confirm="confirmSign">
        </SignModal>
    </a-modal>
</template>

<script>
import radio from '@/assets/radio.png'
import radioChecked from '@/assets/radio_check.png'
import FormItem from '@/components/FormItem.vue'
import CustomSelect from '@/components/CustomSelect.vue'
import SignModal from '@/components/SignModal.vue'
export default {
    name: 'AddressModal',
    props: {
        bh: {
            type: String,
            required: false,
        },
        open: {
            type: Boolean,
            required: true,
        },
        modelValue: {
            type: String,
            required: true,
        },
    },
    components: {
        FormItem,
        CustomSelect,
        SignModal,
    },
    emits: [
        'update:modelValue',
        'update:open',
        'update:bh',
        'confirm',
        'cancel',
    ],
    data() {
        return {
            khhlist: [], //开户行列表
            skzhlist: [],
            activeIndex: null,
            radio: radio,
            radioChecked: radioChecked,
            formData: null,
            baseFormData: {
                // 新增id为空，其他为必填
                bh: '', // id，新增为空
                khhlb: '', // 开户行类别
                khhlbMc: '', // 开户行类别名称
                lhh: '', // 联行号
                lxfs: '', //联系方式
                qmPath: '', // 签名图片路径
                sfbrzh: 1, // 是否本人账户
                skrkhh: '', // 收款人开户行
                skrxm: '', // 收款人姓名
                skrzh: '', // 收款人账户
                zjhm: '', // 证件号码
            },
            sfbrzhCodes: [
                {
                    text: '本人',
                    value: 1,
                },
                { text: '代收人', value: 2 },
                { text: '公司账户', value: 3 },
            ],

            gznr: '1.为便于当事人及时收到人民法院退还的诉讼费用，当事人应当如实提供用于接收诉讼费用退费的准确本人银行账号(一类账户)。\n2.执行审判期间产生执行款的，该账户宜用于接收法院扣划被执行人执行款后支付给申请执行人的账户渠道。\n3.指定的收款人非本案当事人本人，需由当事人本人签名确认，否则不予办理。\n4.当事人确认的收款银行账号适用于案件各个诉讼阶段，包括一审，二审以及再审。\n5.在案件审理期间及人民法院退还费用前，如果收款银行账号有变更的，当事人应及时告知。\n6.如果当事人提供的收款银行账号不准确或者不及时填写变更后的收款银行账号，当事人应自行承担由此产生的一切后果。',
            touchTimer: null,

            signModalOpen: false,
        }
    },
    watch: {
        open(val) {
            if (!val) {
                this.activeIndex = null
            }
        },
    },
    mounted() {
        this.getSkzhList()
        this.getKhhList('0', '', '').then((data) => {
            this.khhlist = data
        })
    },
    methods: {
        chooseSkzh(index) {
            if (this.activeIndex == index) {
                this.activeIndex = null
            } else {
                this.activeIndex = index
            }
        },
        getSkzhList() {
            this.$rpa
                .dealFetch(
                    'https://zxfw.court.gov.cn/yzw/yzw-zxfw-yhfw/api/v1/grxx/skzh/list',
                    {
                        method: 'POST',
                        body: { pageNum: 1, pageSize: 12 },
                    }
                )
                .then((res) => {
                    this.skzhlist = res.data.data
                })
        },
        async getKhhList(khhlb, searchText, lhh) {
            let res = await this.$rpa.dealFetch(
                `https://zxfw.court.gov.cn/yzw/yzw-zxfw-yhfw/api/v1/grxx/skzh/khhList?pcCode=${khhlb}&limit=20&name=${searchText}&lhh=${lhh}`,
                {
                    method: 'GET',
                }
            )
            return res.data
        },
        async edit(item) {
            if (item) {
                this.formData = (
                    await this.$rpa.dealFetch(
                        `https://zxfw.court.gov.cn/yzw/yzw-zxfw-yhfw/api/v1/grxx/skzh/detail?bh=${item.bh}`,
                        {
                            method: 'GET',
                        }
                    )
                ).data
            } else {
                this.formData = JSON.parse(JSON.stringify(this.baseFormData))
                this.formData.sfbrzh = 1
                let userInfo = this.$store.getters.currentUserInfo
                if (userInfo) {
                    this.formData.zjhm = userInfo.zjhm
                    this.formData.skrxm = userInfo.username
                    this.formData.lxfs = userInfo.sjhm
                }
            }
        },
        async del(id) {
            // 是否删除
            await this.$messageConfirm({
                message: '确认删除该收款账号?',
                confirmText: '确定',
                showCancel: true,
                cancelText: '取消',
            })
            await this.$rpa.dealFetch(
                'https://zxfw.court.gov.cn/yzw/yzw-zxfw-yhfw/api/v1/grxx/skzh/del',
                {
                    method: 'POST',
                    body: {
                        bh: id,
                    },
                }
            )
            this.getSkzhList()
        },
        chooseSfbr(item) {
            this.formData.sfbrzh = item.value
            if (this.formData.sfbrzh != 1) {
                this.formData.zjhm = ''
                this.formData.skrxm = ''
                this.formData.lxfs = ''
                return
            }
            let userInfo = this.$store.getters.currentUserInfo
            if (userInfo) {
                this.formData.zjhm = userInfo.zjhm
                this.formData.skrxm = userInfo.username
                this.formData.lxfs = userInfo.sjhm
            }
        },
        updateSkrkhh(value, item) {
            this.formData.skrkhh = item.text
        },

        /**
         * 实际滚动(方向)
         */
        scroll(count) {
            let dom = this.$refs['gznr']
            this.touchTimer = setInterval(() => {
                dom.scrollBy({ behavior: 'smooth', top: 10 * count })
            }, 50) // 每200ms滚动一次
        },
        scrollEnd() {
            clearInterval(this.touchTimer)
        },
        handleAdd() {
            this.formData = JSON.parse(JSON.stringify(this.baseFormData))
        },

        handleCancel() {
            this.$emit('update:open', false)
        },
        confirm() {
            this.$emit('confirm', this.skzhlist[this.activeIndex])
        },
        toList() {
            this.formData = null
        },
        toSign() {
            let requiredProps = [
                'skrxm',
                'zjhm',
                'khhlb',
                'skrkhh',
                'lhh',
                'skrzh',
                'lxfs',
            ]
            if (requiredProps.some((item) => !this.formData[item])) {
                this.$message.warning('请填写完整收款人信息')
                return
            }
            this.signModalOpen = true
        },

        async confirmSign(sign) {
            this.formData.qmpath = sign.osspath
            let res = await this.$rpa.dealFetch(
                `https://zxfw.court.gov.cn/yzw/yzw-zxfw-yhfw/api/v1/grxx/skzh/add`,
                {
                    method: 'POST',
                    body: this.formData,
                }
            )
            this.formData = null
            this.getSkzhList()
            this.signModalOpen = false
        },
    },
}
</script>

<style scoped>
.modal_item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.item_con {
    border-radius: 10px;
    border: 1px solid #dbe6f1;
    margin-left: 10px;
    overflow: hidden;
}

.modal_item.active .item_con {
    border: 1px solid #3173c6;
}

.item_head {
    width: 888px;
    height: 64px;
    background: #f1f5ff;

    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 24px;
    color: #333333;
}

.item_head img {
    margin-left: 20px;
}
.item_content {
    display: flex;
    padding: 20px;
}
.item_content_left {
    width: 250px;
}
.content_title {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 20px;
    color: #a1b1c5;
}
.content_text {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 24px;
    color: #333333;
}
.add_btn {
    width: 102px;
    height: 60px;
    background: #3173c6;
    border-radius: 10px 10px 10px 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 24px;
    color: #ffffff;
    position: absolute;
    left: 50px;
}
</style> 
<!-- 编辑页面样式 -->
<style scoped>
.tabs {
    width: 100%;
    display: flex;
    justify-content: space-between;
}

.tabs .tab {
    width: 316px;
    height: 72px;
    background: #f1f5ff;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 24px;
    color: #3173c6;
    border-radius: 10px 10px 10px 10px;
}

.tabs .tab.active {
    background: #3173c6;
    color: #fff;
}

.address_form {
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    margin-top: 20px;
}

.gznr {
    width: 100%;
    display: flex;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 24px;
    color: #333333;
    margin-top: 20px;
    border: 1px solid #a1b1c5;
    position: relative;
}

.gznr div:first-of-type {
    width: 218px;
    height: 291px;
    background: #f1f5ff;
    border-right: 1px solid #a1b1c5;
}
.gznr div:last-of-type {
    flex: 1;
    height: 291px;
    overflow: auto;
    white-space: pre-wrap;
    padding: 10px;
}

.gznr div:last-of-type::-webkit-scrollbar {
    display: none;
}

.gznr > img {
    width: 40px;
    height: 40px;
    opacity: 0.5;
    position: absolute;
    right: 20px;
}

.gznr > img:first-of-type {
    top: 74px;
}

.gznr > img:last-of-type {
    top: 204px;
}
</style>
<style>
.addressDialog .ant-modal-content {
    padding: 0;
    border-radius: 20px;
    overflow: hidden;
}
.addressDialog .ant-modal-header {
    margin-bottom: 0px;
}
.addressDialog .ant-modal-title {
    height: 100px;
    background: #dbe6f1;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 42px;
    color: #03206a;
    display: flex;
    align-items: center;
    justify-content: center;
}

.addressDialog .modal_content {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 30px;
    color: #333333;
    padding: 20px 50px 10px 50px;
    border-bottom: 2px solid #dbe6f1;
    height: 738px;
    overflow: auto;
}

.addressDialog .modal_footer {
    height: 112px;
    padding: 0 50px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    position: relative;
}

.addressDialog .modal_btn {
    width: 216px;
    height: 72px;
    background: #3173c6;
    border: 1px solid #3173c6;
    border-radius: 10px 10px 10px 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20px;
}

.addressDialog .modal_btn.normal {
    background: #fff;
    color: #3173c6;
}
</style>