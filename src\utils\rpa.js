import { messageConfirm } from '@/plugins/messageConfirm'
import router from '@/router'
import store from '@/store'
import { RPA_API_URL } from '@/config'

/**
 * 调用本机的python rpa工程，封装接口
 */
let url = RPA_API_URL

// 延迟关闭loading
const delayHideLoading = (delay = 100) => {
  setTimeout(() => {
    store.dispatch('loading/setLoading', false)
  }, delay)
}

async function get(url) {
    let res = await baseRpa('/getUrl', {
        url: url
    })
    return res
}

async function resetBrowser() {
    let res = await baseRpa('/advancedResetBrowser')
    return res
}

async function refreshPage() {
    let res = await baseRpa('/refreshPage')
    return res
}

async function getLocalStorage(keyList) {
    let res = await baseRpa('/getLocalStorage', {
        keyList: keyList
    })
    return res
}

async function dealFetch(url, options, loading = true) {
    try {
        // 设置loading状态为true
        if (loading) {
            store.dispatch('loading/setLoading', true)
        }

        if (!options.headers) {
            options.headers = {}
        }
        if (!options.headers['authorization']) {
            options.headers['authorization'] = localStorage.getItem('zxfwtoken')
        }
        if (options.method != 'GET') {
            if (options.body instanceof FormData) {
                // 处理FormData对象以便后端可以处理
                const formDataObj = {
                    _isFormData: true,
                    entries: []
                }

                // 遍历FormData并提取所有的表单项
                for (let pair of options.body.entries()) {
                    if (pair[1] instanceof Blob || pair[1] instanceof File) {
                        // 处理文件/Blob类型
                        const file = pair[1]

                        // OSS策略可能要求'image/jpeg'，但MIME类型可能是'image/jpg'
                        let fileType = file.type
                        if (url.includes('aliyuncs.com') && fileType === 'image/jpg') {
                            console.log("Normalizing content type from image/jpg to image/jpeg for OSS upload.")
                            fileType = 'image/jpeg'
                        }

                        const reader = new FileReader()

                        // 使用Promise等待文件读取完成
                        const fileData = await new Promise((resolve) => {
                            reader.onload = () => {
                                // 提取base64数据部分
                                const base64Data = reader.result.split(',')[1]
                                resolve({
                                    _isFile: true,
                                    name: file.name || 'file',
                                    type: fileType,
                                    data: base64Data
                                })
                            }
                            // 以base64方式读取文件
                            reader.readAsDataURL(file)
                        })

                        formDataObj.entries.push([pair[0], fileData])
                    } else {
                        // 处理普通表单字段
                        formDataObj.entries.push([pair[0], pair[1]])
                    }
                }

                // 替换原始FormData对象
                options.body = formDataObj
                // 如果是OSS上传请求，确保不设置content-type
                if (url.includes('oss-cn-north-2-gov-1.aliyuncs.com')) {
                    delete options.headers['content-type']
                }
            } else {
                if (!options.headers['content-type']) {
                    options.headers['content-type'] = 'application/json'
                }
                options.body = JSON.stringify(options.body)
            }
        }
        let res = await baseRpa('/fetch', {
            requestURL: url,
            requestOptions: options
        })
        if (res.code == 500) {
            if (loading) {
                delayHideLoading()
            }
            await messageConfirm({
                title: '系统异常',
                message: `系统数据异常，错误${res.code}`
            })
            throw new Error('系统数据异常，错误' + res.code)
        }
        if (res.code == 401) {
            if (loading) {
                delayHideLoading()
            }
            await messageConfirm({
                title: '登录超时',
                message: `登录超时，请重新登录`
            })
            router.push('/')
            throw new Error('系统数据异常，错误' + res.code)
        }
        return res
    } catch (error) {
        if (loading) {
            delayHideLoading()
        }
        throw error
    } finally {
        if (loading) {
            delayHideLoading()
        }
    }
}

async function operateElement(elements) {
    let res = await baseRpa('/operateElement', {
        operations: elements
    })
    return res
}

async function getImage(element) {
    let res = await baseRpa('/getImage', element)
    return res
}

async function getText(element) {
    let res = await baseRpa('/getText', element)
    return res
}

async function baseRpa(restUrl, param) {
    let res = await fetch(url + restUrl, {
        method: 'POST',
        headers: {
            'content-type': 'application/json',
        },
        body: JSON.stringify(param),
    })
    let data = await res.json()
    return data
}

export default {
    resetBrowser,
    refreshPage,
    getLocalStorage,
    dealFetch,
    get,
    operateElement,
    getImage,
    getText
}